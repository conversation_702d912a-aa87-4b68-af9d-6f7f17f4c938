import { BrowserRouter as Router, Route, Routes, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import MainPage from './components/MainPage';
import RegistrationForm from './components/RegistrationForm';
import CourseSelection from './components/CourseSelection';
import { SuggestionForm } from './components/SuggestionForm';
import ProfileNew from './components/ProfileNew';
import LoginBox from './components/LoginBox';
import WelcomeScreen from './components/WelcomeScreen';
import UserLogin from './components/UserLogin';
import RegistrationDataA from './components/RegistrationDataA';
import RegistrationDataB from './components/RegistrationDataB';
import Dashboard from './components/Dashboard';
import CourseInfoPage from './components/CourseInfoPage';
import ProfilefromQR from './components/ProfilefromQR';
import Registration from './components/dashboardfiles/Registration';
import Category from './components/dashboardfiles/Category';
import Grades from './components/dashboardfiles/Grades';
import Programs from './components/dashboardfiles/Programs';
import { PaymentConfirmation } from './components/PaymentConfirmation';


const AppContainer = styled.div`
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
  width: 100%;
  min-height: 100vh;
  margin: 0 auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  @media (min-width: 1024px) {
    max-width: 1200px;
  }
`;

function Layout({ children }) {
  const location = useLocation();

  // Define routes that should not use AppContainer
  const fullScreenRoutes = ['/userlogin', '/regdataa', '/regdatab', '/dashboard','/dashboard/registration','/dashboard/category','/dashboard/grades','/dashboard/programs',
    '/dashboard/programmanagement','/dashboard/attendancesheet'
  ];

  // Check if the current path matches the full-screen routes
  const isFullScreen = fullScreenRoutes.includes(location.pathname);

  return isFullScreen ? <>{children}</> : <AppContainer>{children}</AppContainer>;
}

function App() {
  return (
    <div className="min-h-screen bg-neutral-100">
      <Router basename="/wellness">
        <Layout>
          <Routes>
          <Route path="/welcome" element={<WelcomeScreen />} />
          <Route path="/mainpage" element={<MainPage />} />
          <Route path="/register" element={<RegistrationForm />} />
          <Route path="/courseselection" element={<CourseSelection />} />
          <Route path="/thankyou" element={<SuggestionForm />} />
          <Route path="/paymentconfirmation" element={<PaymentConfirmation />} />
          <Route path="/profile" element={<ProfileNew />} />
          <Route path="/profileqr" element={<ProfilefromQR />} />
          <Route path="/login" element={<LoginBox />} />
          <Route path="/userlogin" element={<UserLogin />} />
          <Route path="/regdataa" element={<RegistrationDataA />} />
          <Route path="/regdatab" element={<RegistrationDataB />} />
          <Route path="/course-info/:programId" element={<CourseInfoPage />} />
          <Route path="/dashboard/*" element={<Dashboard />} />
          {/* <Route path="/category" element={<Category />} /> */}
          <Route path="*" element={<h1>404 Not Found</h1>} />
        </Routes>
      </Layout>
    </Router>
    </div>
  );
}

export default App;
