import React, { useRef, useEffect } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { Autoplay } from 'swiper/modules';
import FeaturesSection from './FeaturesSection';
import LoginBox from './LoginBox';
import 'swiper/css';
import useFetchCourseAnalysis from '../hooks/useFetchCourseAnalysis';
import { useNavigate } from 'react-router-dom';
import AsyncStorage from '@react-native-async-storage/async-storage';


const MainPage = () => {  
  const loginRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

const goToLogin = () => {
    navigate("/login", {
      state: {
        fromQR: false, 
        programId: 0 , // carry the courseId here
      },
    });
  };

  const scrollToLogin = () => {
    loginRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const { courseAnalysis, setDataFlag } = useFetchCourseAnalysis("TOP5");

  useEffect(() => {
   AsyncStorage.clear();
  }, []);


  // const cardItems = [
  //   {
  //     title: 'Yoga & Meditation',
  //     image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?w=500&h=500&fit=crop'
  //   },
  //   {
  //     title: 'Nutrition Planning',
  //     image: 'https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=500&h=500&fit=crop'
  //   },
  //   {
  //     title: 'Mental Wellness',
  //     image: 'https://images.unsplash.com/photo-1506126613408-eca07ce68773?w=500&h=500&fit=crop'
  //   },
  //   {
  //     title: 'Physical Fitness',
  //     image: 'https://images.unsplash.com/photo-1517836357463-d25dfeac3438?w=500&h=500&fit=crop'
  //   }
  // ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 via-white to-primary-50 flex flex-col justify-center py-4 md:py-8">
      <div className="w-full max-w-4xl mx-auto px-4 space-y-6 md:space-y-8">
        {/* Logo Box */}
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-primary-lg">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <span
              className="text-5xl md:text-6xl lg:text-7xl font-bold text-secondary-800 animate-float"
              style={{ display: 'inline-block' }}
            >
              w
            </span>
            <span className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-secondary-600 to-secondary-800 bg-clip-text text-transparent">
              Holistic
            </span>
            <span className="text-5xl md:text-6xl lg:text-7xl font-bold bg-gradient-to-r from-accent-600 to-accent-800 bg-clip-text text-transparent">
              Wellness
            </span>
          </div>
          <div>
            <p className="italic text-neutral-700 text-center font-sans text-base md:text-lg leading-relaxed max-w-2xl mx-auto">
              To achieve health, harmony and quality in every aspect of life-Body, Mind, Soul & Family.
            </p>
          </div>
        </div>

        {/* <div ref={loginRef}>
          <LoginBox />
        </div>

        <FeaturesSection onRegisterClick={scrollToLogin} /> */}

        {/* Programs Carousel */}
        <div className="bg-white/95 backdrop-blur-sm rounded-2xl p-6 md:p-8 shadow-primary-lg">
          <h2 className="text-2xl md:text-3xl font-bold text-secondary-800 mb-6 text-center">Top Choices</h2>
          <Swiper
            modules={[Autoplay]}
            spaceBetween={15}
            slidesPerView={1.5}
            centeredSlides={true}
            loop={true}
            autoplay={{
              delay: 2000,
              disableOnInteraction: false,
            }}
            className="w-full"
          >
            {courseAnalysis.map((item) => (
              <SwiperSlide key={item.id}>
                <div className="bg-white rounded-lg overflow-hidden shadow-md">
                  <div className="aspect-square">
                    <img 
                       src={`/assets/${item.course_imagepath}`}
                      alt={item.category}
                      className="w-full h-full object-cover"
                      height="100"
                      width="100"
                    />
                  </div>
                  <div className="p-2">
                    <h3 className="text-md font-semibold text-gray-800">
                      {item.course}
                    </h3>
                  </div>
                </div>
              </SwiperSlide>
            ))}
          </Swiper>
        </div>

        {/* <div className="flex max-w-md pt-3 pb-3 space-x-4">
         
            <button className="flexgrow p-3 text-white bg-blue-600 rounded-lg shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-300"
             onClick={()=> {navigate('/register'); }}// Navigate to the registration page}
            >
                  New User
            </button>
        
            <button className="flex-grow p-3 text-white bg-blue-600 rounded-lg shadow-lg hover:bg-blue-700 hover:shadow-xl transition-all duration-300"
            onClick={()=> {navigate('/login'); }}// Navigate to the login page}
            >
                  Existing User
            </button>
        </div> */}
        <div className="flex flex-col md:flex-row mx-auto pt-6 pb-6 space-y-4 md:space-y-0 md:space-x-6">
          <button
            className="flex-grow p-4 md:p-5 text-white bg-secondary-600 rounded-xl shadow-secondary hover:bg-secondary-700 hover:shadow-secondary transition-all duration-300 font-medium text-lg"
            onClick={goToLogin}
          >
            New User
          </button>
          <button
            className="flex-grow p-4 md:p-5 text-white bg-accent-600 rounded-xl shadow-accent hover:bg-accent-700 hover:shadow-accent transition-all duration-300 font-medium text-lg"
            onClick={goToLogin}
          >
            Existing User
          </button>
        </div>


        {/* <div ref={loginRef}>
          <LoginBox />
        </div> */}

        <FeaturesSection onRegisterClick={scrollToLogin} />

        <div className="flex flex-col justify-center items-center mt-8">
           <p className="text-neutral-600 text-base mb-4">Brought to you by</p>
           <div className="text-center font-serif">
              <a href="https://www.bhaktivedantahospital.com/" className="text-decoration-none hover:opacity-80 transition-opacity">
                 <h2 className="text-2xl md:text-3xl font-bold text-secondary-700">BHAKTIVEDANTA</h2>
                 <p className="text-lg md:text-xl text-secondary-600">HOSPITAL & RESEARCH INSTITUTE</p>
              </a>
           </div>
        </div>
      </div>
    </div>
  );
};

export default MainPage;