// import React, { useState, useEffect } from "react";
// import { useParams, useNavigate } from "react-router-dom";
// import useFetchCourseDetails from "../hooks/useFetchCourseDetails";
// import { getProgramByIdDemo } from "../hooks/managePrograms";
// import AsyncStorage from '@react-native-async-storage/async-storage';
// // import OTPVerificationModal from "./OTPVerificationModal"; // Uncomment if needed


// interface Program {
//   course_imagepath?: string;
//   course_title?: string;
//   course_content?: string;
//   course_category?: string;
//   courseid?:bigint;
// }



// const CourseInfoPage = () => {
//   const { programId } = useParams();
//   const [showOTPModal, setShowOTPModal] = useState(false);
//   const navigate = useNavigate();
//   const [course, setCourse] = useState<Program | null>(null);
  

//   // const { course, isLoading, error } = useFetchCourseDetails(Number(courseId));
 
  

// //   const handleOTPVerified = (mobile: string, delegateData: any) => {
// //     navigate("/register", {
// //       state: {
// //         fromQR: true,
// //         courseId,
// //         profileData: delegateData || null,
// //         mobileFromQR: mobile,
// //       },
// //     });
// //   };

// const goToLogin = () => {
//     navigate("/login", {
//       state: {
//         fromQR: true, 
//         programId: programId, // carry the courseId here
//       },
//     });
//   };

//  useEffect(() => {
//     if (programId) {
//       AsyncStorage.clear();
//       getProgramByIdDemo(programId)
//         .then(data => setCourse(data))
//         .catch(err => setCourse(null));
//     }
//   }, [programId]);

//   // if (isLoading) return <div className="text-center">Loading course...</div>;
//   // if (error || !course) return <div className="text-center text-red-500">Failed to load course info.</div>;

//   if (!course) {
//     return <div className="text-center">Loading course...</div>;
//   }


//   return (
//     <div className="max-w-xl mx-auto p-4 bg-white rounded shadow">
//       <img
//         src={`/assets/${course.course_imagepath}`}
//         alt={course.course_title}
//         className="rounded mb-4"
//       />
//       <h1 className="text-2xl font-bold mb-2">{course.course_title}</h1>
//       <p className="text-gray-700 mb-6">{course.course_content}</p>
//       <p className="text-gray-700 mb-6">{course.scheduledate}</p>
//       <p className="text-gray-700 mb-6">{course.scheduletime}</p>
//       <p className="text-gray-700 mb-6">{course.speakername}</p>
//       <p className="text-gray-700 mb-6">{course.venue}</p>
//       <p className="text-gray-700 mb-6">{course.coursefees}</p>

//       <button
//        onClick={goToLogin}
//         className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
//       >
//         Register for this Course
//       </button>
      
//     </div>
//   );
// };

// export default CourseInfoPage;


import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import AsyncStorage from '@react-native-async-storage/async-storage';
import { getProgramByIdDemo } from "../hooks/managePrograms";

interface Program {
  course_imagepath?: string;
  course_title?: string;
  course_content?: string;
  course_category?: string;
  scheduledate?: string;
  scheduletime?: string;
  speakername?: string;
  venue?: string;
  coursefees?: number | string;
  slots_left?: number;  // Add this if available
  slots_total?: number; // Add this if available
}

const formatDate = (isoString?: string) => {
  if (!isoString) return "";
  const d = new Date(isoString);
  return d.toLocaleDateString("en-IN", { day: "numeric", month: "short", year: "numeric" });
};

const CourseInfoPage = () => {
  const { programId } = useParams();
  const navigate = useNavigate();
  const [course, setCourse] = useState<Program | null>(null);

  useEffect(() => {
    if (programId) {
      AsyncStorage.clear();
      getProgramByIdDemo(programId)
        .then(data => setCourse(data))
        .catch(() => setCourse(null));
    }
  }, [programId]);

  const goToLogin = () => {
    navigate("/login", {
      state: { fromQR: true, programId },
    });
  };

const formatTime = (timeString?: string) => {
  if (!timeString) return "";
  // Handles cases where time comes as "15:00:00" or as an ISO string
  let dateObj;
  if (timeString.length > 8) {
    dateObj = new Date(timeString);
  } else {
    // Attach to today's date
    const today = new Date();
    const [h, m, s] = timeString.split(":");
    dateObj = new Date(today.getFullYear(), today.getMonth(), today.getDate(), Number(h), Number(m), Number(s || 0));
  }
  return dateObj.toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    hour12: true,
  });
};


  // Animated pulse for register button
  const pulseAnim =
    "animate-[pulse_2s_infinite] shadow-lg shadow-blue-200/40";

  // For slots progress bar
  const slots_left = course?.slots_left || 8;
  const slots_total = course?.slots_total || 20;
  const slots_percent = Math.round((slots_left / slots_total) * 100);

  if (!course) {
    return <div className="text-center py-10">Loading course...</div>;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-neutral-50 to-primary-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md md:max-w-lg bg-white rounded-3xl shadow-xl overflow-hidden border border-neutral-200 relative transition-transform duration-300 hover:scale-105 hover:shadow-2xl group animate-fade-in">
        {/* Floating Price Badge */}
        <div className="absolute top-6 right-6 z-10">
          <div className="bg-gradient-to-r from-secondary-600 to-secondary-700 text-white font-bold px-5 py-2 rounded-xl shadow-lg text-lg border-2 border-white">
            ₹ {course.coursefees}
          </div>
        </div>
        {/* Verified Badge */}
        <div className="absolute top-6 left-6 flex items-center gap-1">
          <span className="bg-accent-100 text-accent-700 px-3 py-1 rounded-full text-xs font-semibold flex items-center gap-1 shadow-sm">
            <svg width="16" height="16" className="inline" fill="none" viewBox="0 0 24 24"><circle cx="12" cy="12" r="12" fill="currentColor"/><path d="M8 12l2.5 2.5 5-5" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
            Verified
          </span>
        </div>

        {/* Program Image */}
        <div className="overflow-hidden">
          <img
            src={`/assets/${course.course_imagepath}`}
            alt={course.course_title}
            className="h-64 md:h-80 w-full object-cover object-center scale-100 group-hover:scale-105 transition-transform duration-500"
          />
        </div>

        {/* Details Section */}
        <div className="p-6 pt-4">
          {/* Category chip */}
          {course.course_category && (
            <span className="inline-block bg-accent-100 text-accent-800 text-xs font-bold rounded-full px-4 py-1 mb-2 uppercase tracking-widest shadow">
              {course.course_category}
            </span>
          )}

          <h1 className="text-2xl md:text-3xl font-extrabold text-secondary-800 mb-1 mt-1 leading-tight">
            {course.course_title}
          </h1>
          <div className="flex items-center gap-1 mb-3">
            {/* Stars for trust */}
            <span className="text-accent-500 text-lg">★★★★★</span>
            <span className="ml-1 text-xs text-neutral-500">4.9 (248 reviews)</span>
          </div>
          <p className="text-neutral-700 mb-4 text-base leading-relaxed">{course.course_content}</p>

          {/* Info grid with icons */}
          <div className="grid grid-cols-1 gap-3 text-sm mb-6">
            <div className="flex items-center gap-2 text-neutral-600">
              <span role="img" aria-label="calendar" className="text-lg">📅</span>
              <span className="font-semibold">Date:</span>
              <span>{formatDate(course.scheduledate)}</span>
              <span className="ml-4 font-semibold">Time:</span>
              <span>{formatTime(course.scheduletime)}</span>
            </div>
            <div className="flex items-center gap-2 text-neutral-600">
              <span role="img" aria-label="speaker" className="text-lg">🧑‍💼</span>
              <span className="font-semibold">Speaker:</span>
              <span>{course.speakername}</span>
            </div>
            <div className="flex items-center gap-2 text-neutral-600">
              <span role="img" aria-label="venue" className="text-lg">📍</span>
              <span className="font-semibold">Venue:</span>
              <span>{course.venue}</span>
            </div>
          </div>

          {/* Slots left bar */}
          <div className="mb-6">
            <div className="flex justify-between mb-2 text-sm">
              <span className="font-semibold text-neutral-700">
                Slots Left: <span className="text-secondary-700">{slots_left}</span> / {slots_total}
              </span>
              <span className={`font-bold ${slots_percent < 30 ? 'text-red-600' : 'text-accent-600'}`}>
                {slots_percent}% Available
              </span>
            </div>
            <div className="w-full bg-neutral-200 rounded-full h-3">
              <div
                className={`h-3 rounded-full transition-all duration-700 ${slots_percent < 30 ? "bg-red-500" : "bg-secondary-500"}`}
                style={{ width: `${slots_percent}%` }}
              />
            </div>
          </div>

          {/* Register Button */}
          <button
            onClick={goToLogin}
            className={`w-full bg-gradient-to-r from-secondary-600 to-secondary-700 hover:from-secondary-700 hover:to-secondary-800 text-white font-bold py-4 rounded-2xl text-lg mt-3 focus:outline-none focus:ring-4 focus:ring-secondary-200 transition-all duration-200 ${pulseAnim}`}
            style={{ boxShadow: "0 6px 20px 0 rgba(30,64,175,0.15)" }}
          >
            <span role="img" aria-label="register">🚀</span> Register Now
          </button>
        </div>
      </div>

      {/* Sticky Register Button for Mobile */}
      <div className="fixed bottom-0 left-0 right-0 z-30 md:hidden p-4 bg-gradient-to-t from-white/95 to-transparent pointer-events-none">
        <button
          onClick={goToLogin}
          className={`w-full bg-secondary-600 hover:bg-secondary-700 text-white font-bold py-4 rounded-2xl shadow-xl text-lg transition-all duration-200 pointer-events-auto ${pulseAnim}`}
        >
          🚀 Register Now
        </button>
      </div>
      <style>
        {`
        @keyframes pulse {
          0%, 100% { box-shadow: 0 0 0 0 rgba(30,64,175,0.4); }
          50% { box-shadow: 0 0 20px 8px rgba(30,64,175,0.20); }
        }
        .animate-fade-in {
          animation: fadeIn 1s ease;
        }
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(32px);}
          to { opacity: 1; transform: none;}
        }
        `}
      </style>
    </div>
  );
};

export default CourseInfoPage;
