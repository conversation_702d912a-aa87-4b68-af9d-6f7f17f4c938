import React, { useState, useRef, useEffect } from "react";
import Card from "./Card";
import { CheckSquare, Square, Send } from "lucide-react";
import useFetchCourses from "../hooks/useFetchCourses";
import { postCourseSelected } from "../hooks/manageDelegates";
import { useNavigate } from "react-router-dom";
import { Button, Card as UICard, CardContent, Typography } from './ui';

interface ProgramCardProps {
  course_id: bigint;
  course_code?: string;
  course_title: string;
  course_shortdetail?: string;
  course_content: string;
  course_category?: string;
  course_imagepath: string;
}

interface Course extends ProgramCardProps {
  prevselected?: number;
}

const CourseSelection: React.FC = () => {
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [currentScreen, setCurrentScreen] = useState(0);
  const delegateId = localStorage.getItem("delegateId") || "";
  const { courses, isLoading, error } = useFetchCourses(delegateId);
  const navigate = useNavigate();
  const headerref = useRef<HTMLDivElement>(null);

  const screenTitles = [
    "Family Wellness",
    "Physical Wellness(Body)",
    "Mental Wellness(Mind)",
    "Spiritual Wellness(Soul)",
  ];

  // Preselect courses with prevselected = 1
  useEffect(() => {
    if (courses && courses.length > 0) {
      const initiallySelected = courses
        .filter((course) => course.prevselected === 1)
        .map((course) => course.course_id.toString());
      setSelectedCards(initiallySelected);
    }
  }, [courses]);

  const scrollToHeader = () => {
    headerref.current?.scrollIntoView({ behavior: "smooth" });
  };

  const toggleSelect = (id: string) => {
    setSelectedCards((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((cardId) => cardId !== id)
        : [...prevSelected, id]
    );
  };

  const handleSubmit = () => {
    if (selectedCards.length === 0) {
      alert("Please select at least one program before submitting.");
      return;
    }
    postCourseSelected(delegateId, selectedCards)
      .then(() => navigate("/thankyou"))
      .catch((error) => alert("Submission failed: " + error.message));
  };

  const ProgramCard: React.FC<ProgramCardProps> = ({
    course_id,
    course_title,
    course_content,
    course_imagepath,
  }) => {
    const isSelected = selectedCards.includes(course_id.toString());

    return (
      <UICard
        variant="default"
        hover
        className={`flex overflow-hidden ${
          isSelected ? "ring-2 ring-secondary-500 shadow-secondary" : ""
        }`}
      >
        <div className="p-4 flex-shrink-0">
          <div className="relative w-[120px] h-[120px] sm:w-[140px] sm:h-[140px] flex-shrink-0">
            <img
              src={`/assets/${course_imagepath}`}
              alt={course_title}
              className="w-full h-full object-cover rounded-xl"
              loading="lazy"
            />
          </div>
        </div>
        <div className="flex-1 p-4 flex flex-col gap-3">
          <div>
            <Typography variant="h6" weight="bold" className="mb-1">
              {course_title}
            </Typography>
            <Typography variant="body2" color="neutral">
              {course_content}
            </Typography>
          </div>
          <Button
            onClick={() => toggleSelect(course_id.toString())}
            variant={isSelected ? "primary" : "secondary"}
            size="sm"
            fullWidth
            className="mt-auto"
          >
            {isSelected ? "Selected" : "Select"}
          </Button>
        </div>
      </UICard>
    );
  };

  const renderCategory = (category: string) => (
    <div className="grid grid-cols-1 gap-4 px-4">
      {courses
        .filter((course) => course.course_category === category)
        .map((course) => (
          <ProgramCard key={course.course_id.toString()} {...course} />
        ))}
    </div>
  );

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 py-4 space-y-6">
          {isLoading && (
            <div className="text-center">
              <Typography variant="body1" color="neutral">Loading courses...</Typography>
            </div>
          )}
          {error && (
            <div className="text-center">
              <Typography variant="body1" color="error">
                Failed to load courses. Please try again.
              </Typography>
            </div>
          )}
          {!isLoading && !error && (
            <>
              <div className="flex items-center justify-center py-6">
                <UICard variant="primary" className="px-8 py-4">
                  <Typography
                    variant="h3"
                    weight="extrabold"
                    color="primary"
                    align="center"
                    ref={headerref}
                  >
                    {screenTitles[currentScreen]}
                  </Typography>
                </UICard>
              </div>
              {renderCategory(screenTitles[currentScreen])}
            </>
          )}
        </div>

      {/* Bottom Navigation */}
      <div className="bg-white border-t border-neutral-200 py-4 flex justify-between px-6 mt-auto">
        <Button
          disabled={currentScreen === 0}
          onClick={() => {
            scrollToHeader();
            setCurrentScreen((prev) => prev - 1);
          }}
          variant={currentScreen === 0 ? "ghost" : "secondary"}
          size="md"
        >
          Back
        </Button>
        {currentScreen < screenTitles.length - 1 ? (
          <Button
            onClick={() => {
              scrollToHeader();
              setCurrentScreen((prev) => prev + 1);
            }}
            variant="primary"
            size="md"
          >
            Next
          </Button>
        ) : (
          <Button
            onClick={handleSubmit}
            variant="accent"
            size="md"
            rightIcon={<Send className="w-4 h-4" />}
          >
            Submit
          </Button>
        )}
      </div>
    </div>
  );
};

export default CourseSelection;


// // import React, { useState } from "react";
// // import Card from "./Card";
// // import { CheckSquare, Square, Send } from "lucide-react";
// // import useFetchCourses from "../hooks/useFetchCourses";
// // import { postCourseSelected } from "../hooks/manageDelegates";
// // import { useNavigate } from "react-router-dom";
// // import FixedHeader from "./FixedHeader";

// // interface ProgramCardProps {
// //   course_id: bigint;
// //   course_code?: string;
// //   course_title: string;
// //   course_shortdetail?: string;
// //   course_content: string;
// //   course_category?: string;
// //   course_imagepath: string;
// // }

// // interface Course {
// //   course_id: bigint;
// //   course_category: string;
// //   course_title: string;
// //   course_content: string;
// //   course_imagepath: string;
// // }

// // const CourseSelection: React.FC = () => {
// //   const [selectedCards, setSelectedCards] = useState<string[]>([]);
// //   const delegateId = localStorage.getItem("delegateId") || "";
// //   const { courses, isLoading, error } = useFetchCourses(delegateId);
// //   const navigate = useNavigate();

// //   // Toggle selection of a card
// //   const toggleSelect = (id: string) => {
// //     setSelectedCards((prevSelected) =>
// //       prevSelected.includes(id)
// //         ? prevSelected.filter((cardId) => cardId !== id)
// //         : [...prevSelected, id]
// //     );
// //   };

// //   // Submit selected courses
// //   const handleSubmit = () => {
// //     if (selectedCards.length === 0) {
// //       alert("Please select at least one program before submitting.");
// //       return;
// //     }
// //     postCourseSelected(delegateId, selectedCards)
// //       .then(() => navigate("/thankyou"))
// //       .catch((error) => alert("Submission failed: " + error.message));
// //   };

// //   // Render individual program card
// //   const ProgramCard: React.FC<ProgramCardProps> = ({
// //     course_id,course_code,course_title,course_shortdetail,course_content,course_category,course_imagepath
// //   }) => {
// //     const isSelected = selectedCards.includes(course_id.toString());

// //     return (
// //       // <div
// //       //   className={`bg-white rounded-lg shadow-md overflow-hidden transition-all duration-300 h-full ${
// //       //     isSelected ? "ring-2 ring-blue-500" : ""
// //       //   }`}
// //       // >
// //       //   <div className="aspect-square w-full">
// //       //     <img
// //       //       src={`/assets/${course_imagepath}`}
// //       //       alt={course_title}
// //       //       className="w-full h-full object-cover"
// //       //       loading="lazy"
// //       //     />
// //       //   </div>
// //       //   <div className="p-2">
// //       //     <div className="flex items-start gap-2">
// //       //       <button
// //       //         onClick={() => toggleSelect(course_id.toString())}
// //       //         className="mt-0.5 flex-shrink-0 text-blue-500 hover:text-blue-600 transition-colors"
// //       //         aria-label={isSelected ? "Unselect program" : "Select program"}
// //       //       >
// //       //         {isSelected ? (
// //       //           <CheckSquare className="w-5 h-5" />
// //       //         ) : (
// //       //           <Square className="w-5 h-5" />
// //       //         )}
// //       //       </button>
// //       //       <div className="min-w-0 flex-1">
// //       //         <h3 className="text-base font-bold text-gray-800 mb-0.5 truncate">
// //       //           {course_title}
// //       //         </h3>
// //       //         <p className="text-sm text-gray-600 leading-snug line-clamp-2">
// //       //           {course_content}
// //       //         </p>
// //       //       </div>
// //       //     </div>
// //       //   </div>
// //       // </div>

// //       //flex-col sm:flex-row
// //       <div 
// //   className={`bg-white rounded-xl shadow-xl hover:shadow-2xl overflow-hidden transition-all duration-300 hover:-translate-y-1 flex  ${
// //     isSelected ? 'ring-2 ring-blue-500' : ''
// //   }`}>
// // <div className="p-4 flex-shrink-0">
// // <div className="relative w-full h-[140px] sm:min-w-[140px] sm:w-[140px] sm:h-[140px] flex-grow">
// // <img 
// //         src={`/assets/${course_imagepath}`}
// //         alt={course_title}
// //         className="w-full h-full object-cover rounded-lg"
// //         loading="lazy"
// //       />
// // </div>
// // </div>
// // <div className="flex-1 p-4 flex flex-col min-w-0 gap-3"> 
// // <div className="flex-1 min-w-0">
// // <h3 className="text-base font-bold text-gray-800 mb-1 break-words">
// //         {course_title}
// // </h3>
// // <p className="text-gray-600 text-sm leading-snug break-words">
// //         {course_content}
// // </p>
// // </div>
// // <button 
// //       onClick={() => toggleSelect(course_id.toString())}
// //       className={`w-full py-1.5 px-3 rounded-lg font-medium text-sm transition-colors mt-auto ${
// //         isSelected 
// //           ? 'bg-blue-600 text-white hover:bg-blue-700' 
// //           : 'bg-blue-100 text-blue-600 hover:bg-blue-200'
// //       }`}
// // >
// //       {isSelected ? 'Selected' : 'Select'}
// // </button>
// // </div>
// // </div>
// //     );
// //   };

// //   // Render a category section
// //   const renderCategory = (title: string, category: string) => (
// //     <>
// //       <div className="flex justify-center px-4">
// //         <Card title={title} />
// //       </div>
// //       <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-7xl mx-auto px-4">
// //       {/* <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 max-w-7xl mx-auto px-3 sm:px-4"> */}
// //         {courses
// //           .filter((course) => course.course_category === category)
// //           .map((course) => (
// //             <ProgramCard course_id={0n} course_title={""} course_content={""} course_imagepath={""} key={course.course_id} {...course} />
// //           ))}
// //       </div>
// //     </>
// //   );

// //   return (
// //     <div className="pt-12 pb-12">
// //       <FixedHeader />

      
// //       <div className="min-h-screen bg-gradient-to-br from-[#FFDAB9] to-[#556B2F] py-4 sm:py-6">
// //         <div className="container mx-auto space-y-6 sm:space-y-8">
// //           {isLoading && (
// //             <div className="text-center text-gray-600">Loading courses...</div>
// //           )}
// //           {error && (
// //             <div className="text-center text-red-500">
// //               Failed to load courses. Please try again.
// //             </div>
// //           )}
// //           {!isLoading && !error && (
// //             <>
// //               {renderCategory("Family Wellness", "Family Wellness")}
// //               {renderCategory("Physical Wellness(Body)", "Physical Wellness(Body)")}
// //               {renderCategory("Mental Wellness(Mind)", "Mental Wellness(Mind)")}
// //               {renderCategory("Spiritual Wellness(Soul)", "Spiritual Wellness(Soul)")}
// //             </>
// //           )}
          
// //         </div>
// //       </div>

// //       <div className=" fixed bottom-0 left-0 right-0 bg-white/25 shadow-md py-4 flex justify-center">
// //             <button
// //               onClick={handleSubmit}
// //               className="w-[90vw] xs:w-[80vw] sm:w-[600px] md:w-[700px] lg:w-[800px] 
// //                 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700
// //                 text-white font-semibold py-3 px-6 rounded-lg shadow-lg
// //                 transform transition-all duration-300 hover:scale-[1.02]
// //                 flex items-center justify-center gap-2"
// //             >
// //               <span className="text-lg">Submit Programs</span>
// //               <Send className="w-5 h-5" />
// //             </button>
// //           </div>


// //     </div>
// //   );
// // };

// // export default CourseSelection;

// import React, { useState, useRef } from "react";
// import Card from "./Card";
// import { CheckSquare, Square, Send } from "lucide-react";
// import useFetchCourses from "../hooks/useFetchCourses";
// import { postCourseSelected } from "../hooks/manageDelegates";
// import { useNavigate } from "react-router-dom";
// //import FixedHeader from "./FixedHeader";

// interface ProgramCardProps {
//   course_id: bigint;
//   course_code?: string;
//   course_title: string;
//   course_shortdetail?: string;
//   course_content: string;
//   course_category?: string;
//   course_imagepath: string;
// }

// interface Course {
//   course_id: bigint;
//   course_category: string;
//   course_title: string;
//   course_content: string;
//   course_imagepath: string;
// }

// const CourseSelection: React.FC = () => {
//   const [selectedCards, setSelectedCards] = useState<string[]>([]);
//   const [currentScreen, setCurrentScreen] = useState(0); // To track current screen
//   const delegateId = localStorage.getItem("delegateId") || "";
//   const { courses, isLoading, error } = useFetchCourses(delegateId);
//   const navigate = useNavigate();


//    const headerref = useRef<HTMLDivElement>(null);


//    const scrolltoheader = () => {
//     headerref.current?.scrollIntoView({ behavior: 'smooth' });
//   };

//   const screenTitles = [
//     "Family Wellness",
//     "Physical Wellness(Body)",
//     "Mental Wellness(Mind)",
//     "Spiritual Wellness(Soul)",
//   ];

//   // Toggle selection of a card
//   const toggleSelect = (id: string) => {
//     setSelectedCards((prevSelected) =>
//       prevSelected.includes(id)
//         ? prevSelected.filter((cardId) => cardId !== id)
//         : [...prevSelected, id]
//     );
//   };

//   // Submit selected courses
//   const handleSubmit = () => {
//     if (selectedCards.length === 0) {
//       alert("Please select at least one program before submitting.");
//       return;
//     }
//     postCourseSelected(delegateId, selectedCards)
//       .then(() => navigate("/thankyou"))
//       .catch((error) => alert("Submission failed: " + error.message));
//   };

//   // Render individual program card
//   const ProgramCard: React.FC<ProgramCardProps> = ({
//     course_id,
//     course_title,
//     course_content,
//     course_imagepath,
//   }) => {
//     const isSelected = selectedCards.includes(course_id.toString());

//     return (
//       <div
//   className={`bg-white rounded-xl shadow-xl hover:shadow-2xl overflow-hidden transition-all duration-300 hover:-translate-y-1 flex ${
//     isSelected ? "ring-2 ring-blue-500" : ""
//   }`}
// >
//   <div className="p-4 flex-shrink-0">
//     <div className="relative w-[120px] h-[120px] sm:w-[140px] sm:h-[140px] flex-shrink-0">
//       <img
//         src={`/assets/${course_imagepath}`}
//         alt={course_title}
//         className="w-full h-full object-cover rounded-lg"
//         loading="lazy"
//       />
//     </div>
//   </div>
//   <div className="flex-1 p-4 flex flex-col gap-3">
//     <div>
//       <h3 className="text-base font-bold text-gray-800 mb-1">
//         {course_title}
//       </h3>
//       <p className="text-sm text-gray-600 leading-snug">
//         {course_content}
//       </p>
//     </div>
//     <button
//       onClick={() => toggleSelect(course_id.toString())}
//       className={`w-full py-1.5 px-3 rounded-lg font-medium text-sm transition-colors mt-auto ${
//         isSelected
//           ? "bg-blue-600 text-white hover:bg-blue-700"
//           : "bg-blue-100 text-blue-600 hover:bg-blue-200"
//       }`}
//     >
//       {isSelected ? "Selected" : "Select"}
//     </button>
//   </div>
// </div>

//     );
//   };

//   // Render a category section
//   const renderCategory = (category: string) => (
//     <div className="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-1 gap-4 max-w-7xl mx-auto px-4">
//       {courses
//         .filter((course) => course.course_category === category)
//         .map((course) => (
//           <ProgramCard
//             course_id={0n}
//             course_title={""}
//             course_content={""}
//             course_imagepath={""}
//             key={course.course_id}
//             {...course}
//           />
//         ))}
//     </div>
//   );

//   return (
//     <div className="pb-12 bg-gradient-to-br from-[#FFDAB9] to-[#556B2F]">
//       {/* <FixedHeader /> */}

//       <div className="min-h-screen bg-gradient-to-br from-[#FFDAB9] to-[#556B2F] py-4 sm:py-6">
       
//         <div className="container mx-auto space-y-6 sm:space-y-8">
//         {/* <FixedHeader /> */}
//           {isLoading && (
//             <div className="text-center text-gray-600">Loading courses...</div>
//           )}
//           {error && (
//             <div className="text-center text-red-500">
//               Failed to load courses. Please try again.
//             </div>
//           )}
//           {!isLoading && !error && (
//             <>
//               {/* Render current screen category */}
//               <div className="flex items-center justify-center py-6">
//                 <div className="relative bg-gradient-to-r from-blue-100 via-white to-blue-100 px-8 py-4 shadow-lg rounded-lg">
//                   <h2 className="text-3xl font-extrabold text-blue-800" ref={headerref}>
//                     {screenTitles[currentScreen]}
//                   </h2>
//                   <div className="absolute inset-0 rounded-lg border-2 border border-blue-300 pointer-events-none"></div>
//                 </div>
//               </div>

//               {renderCategory(screenTitles[currentScreen])}
//             </>
//           )}
//         </div>
//       </div>

//       {/* Navigation Buttons */}
//       <div className="fixed bottom-0 left-0 right-0 bg-white/25 shadow-md py-4 flex justify-between px-6">
//         <button
//           disabled={currentScreen === 0}
//           onClick={() => {
//             scrolltoheader();
//             setCurrentScreen((prev) => prev - 1);
//           }}
//           className={`py-2 px-4 rounded-lg font-medium ${
//             currentScreen === 0
//               ? "bg-gray-300 text-gray-600 cursor-not-allowed"
//               : "bg-blue-500 text-white hover:bg-blue-600"
//           }`}
//         >
//           Back
//         </button>
//         {currentScreen < screenTitles.length - 1 ? (
//           <button
//             onClick={() => {
//               scrolltoheader();
//               setCurrentScreen((prev) => prev + 1)
//             }}
//             className="py-2 px-4 rounded-lg bg-blue-500 text-white hover:bg-blue-600 font-medium"
//           >
//             Next
//           </button>
//         ) : (
//           <button
//             onClick={handleSubmit}
//             className="py-2 px-4 rounded-lg bg-green-500 text-white hover:bg-green-600 font-medium"
//           >
//             Submit
//           </button>
//         )}
//       </div>
//     </div>
//   );
// };

// export default CourseSelection;
