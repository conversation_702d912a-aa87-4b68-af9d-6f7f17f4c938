import React, { useState, useRef, useEffect } from "react";
import Card from "./Card";
import { CheckSquare, Square, Send } from "lucide-react";
import useFetchCourses from "../hooks/useFetchCourses";
import { postCourseSelected } from "../hooks/manageDelegates";
import { useNavigate } from "react-router-dom";
import { Button, Card as UICard, CardContent, Typography } from './ui';

interface ProgramCardProps {
  course_id: bigint;
  course_code?: string;
  course_title: string;
  course_shortdetail?: string;
  course_content: string;
  course_category?: string;
  course_imagepath: string;
}

interface Course extends ProgramCardProps {
  prevselected?: number;
}

const CourseSelection: React.FC = () => {
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [currentScreen, setCurrentScreen] = useState(0);
  const delegateId = localStorage.getItem("delegateId") || "";
  const { courses, isLoading, error } = useFetchCourses(delegateId);
  const navigate = useNavigate();
  const headerref = useRef<HTMLDivElement>(null);

  const screenTitles = [
    "Family Wellness",
    "Physical Wellness(Body)",
    "Mental Wellness(Mind)",
    "Spiritual Wellness(Soul)",
  ];

  // Preselect courses with prevselected = 1
  useEffect(() => {
    if (courses && courses.length > 0) {
      const initiallySelected = courses
        .filter((course) => course.prevselected === 1)
        .map((course) => course.course_id.toString());
      setSelectedCards(initiallySelected);
    }
  }, [courses]);

  const scrollToHeader = () => {
    headerref.current?.scrollIntoView({ behavior: "smooth" });
  };

  const toggleSelect = (id: string) => {
    setSelectedCards((prevSelected) =>
      prevSelected.includes(id)
        ? prevSelected.filter((cardId) => cardId !== id)
        : [...prevSelected, id]
    );
  };

  const handleSubmit = () => {
    if (selectedCards.length === 0) {
      alert("Please select at least one program before submitting.");
      return;
    }
    postCourseSelected(delegateId, selectedCards)
      .then(() => navigate("/thankyou"))
      .catch((error) => alert("Submission failed: " + error.message));
  };

  const ProgramCard: React.FC<ProgramCardProps> = ({
    course_id,
    course_title,
    course_content,
    course_imagepath,
  }) => {
    const isSelected = selectedCards.includes(course_id.toString());

    return (
      <UICard
        variant="default"
        hover
        className={`${
          isSelected ? "ring-2 ring-secondary-500 shadow-secondary" : ""
        }`}
      >
        <div className="p-4">
          <div className="flex gap-4">
            {/* Image */}
            <div className="flex-shrink-0">
              <div className="w-20 h-20 rounded-lg overflow-hidden">
                <img
                  src={`/assets/${course_imagepath}`}
                  alt={course_title}
                  className="w-full h-full object-cover"
                  loading="lazy"
                />
              </div>
            </div>

            {/* Content */}
            <div className="flex-1 min-w-0">
              <Typography variant="h6" weight="bold" className="mb-2 line-clamp-2">
                {course_title}
              </Typography>
              <Typography variant="body2" color="neutral" className="mb-3 line-clamp-3">
                {course_content}
              </Typography>
              <Button
                onClick={() => toggleSelect(course_id.toString())}
                variant={isSelected ? "primary" : "secondary"}
                size="sm"
                fullWidth
              >
                {isSelected ? "Selected" : "Select"}
              </Button>
            </div>
          </div>
        </div>
      </UICard>
    );
  };

  const renderCategory = (category: string) => (
    <div className="space-y-3">
      {courses
        .filter((course) => course.course_category === category)
        .map((course) => (
          <ProgramCard key={course.course_id.toString()} {...course} />
        ))}
    </div>
  );

  return (
    <div className="h-full flex flex-col overflow-hidden">
      {/* Header */}
      <div className="flex-shrink-0 p-4">
        <UICard variant="primary" className="px-6 py-3">
          <Typography
            variant="h4"
            weight="bold"
            color="primary"
            align="center"
            ref={headerref}
          >
            {screenTitles[currentScreen]}
          </Typography>
        </UICard>
      </div>

      {/* Content Area */}
      <div className="flex-1 overflow-y-auto px-4 pb-4">
        {isLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="neutral">Loading courses...</Typography>
          </div>
        )}
        {error && (
          <div className="text-center py-8">
            <Typography variant="body1" color="error">
              Failed to load courses. Please try again.
            </Typography>
          </div>
        )}
        {!isLoading && !error && (
          <div className="space-y-4">
            {renderCategory(screenTitles[currentScreen])}
          </div>
        )}
      </div>

      {/* Bottom Navigation */}
      <div className="flex-shrink-0 bg-white border-t border-neutral-200 p-4">
        <div className="flex justify-between gap-4">
          <Button
            disabled={currentScreen === 0}
            onClick={() => {
              scrollToHeader();
              setCurrentScreen((prev) => prev - 1);
            }}
            variant={currentScreen === 0 ? "ghost" : "secondary"}
            size="md"
            className="flex-1"
          >
            Back
          </Button>
          {currentScreen < screenTitles.length - 1 ? (
            <Button
              onClick={() => {
                scrollToHeader();
                setCurrentScreen((prev) => prev + 1);
              }}
              variant="primary"
              size="md"
              className="flex-1"
            >
              Next
            </Button>
          ) : (
            <Button
              onClick={handleSubmit}
              variant="accent"
              size="md"
              rightIcon={<Send className="w-4 h-4" />}
              className="flex-1"
            >
              Submit
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CourseSelection;
