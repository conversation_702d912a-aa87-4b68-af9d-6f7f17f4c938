import React from "react";
import { Routes, Route, NavLink } from "react-router-dom";
import Registration from "./dashboardfiles/Registration";
import Category from "./dashboardfiles/Category";
import Grades from "./dashboardfiles/Grades";
import Programs from "./dashboardfiles/Programs";
import ProgramManagement from "./dashboardfiles/ProgramManagement";
import AttendanceSheet from "./dashboardfiles/Attendance";
import { Typography } from './ui';
import "tailwindcss/tailwind.css";

const Dashboard: React.FC = () => {
  return (
    <div className="flex h-screen bg-neutral-50">
      {/* Sidebar */}
      <div className="w-64 bg-wellness-800 text-white flex flex-col shadow-xl">
        <div className="p-6 border-b border-wellness-700">
          <Typography variant="h4" className="text-white font-bold">
            Dashboard
          </Typography>
        </div>
        <div className="p-4 border-b border-wellness-700">
          <Typography variant="body2" className="text-wellness-100">
            Logged in as: <span className="font-semibold text-white">Admin User</span>
          </Typography>
        </div>
        <nav className="flex-grow py-4">
          {[
            { name: "Registration", path: "/dashboard/registration" },
            { name: "Category", path: "/dashboard/category" },
            { name: "Grades", path: "/dashboard/grades" },
            { name: "Programs", path: "/dashboard/programs" },
            { name: "Program Management", path: "/dashboard/programmanagement" },
            { name: "Attendance", path: "/dashboard/attendancesheet" }
          ].map(({ name, path }) => (
            <NavLink
              key={path}
              to={path}
              className={({ isActive }) =>
                `block px-6 py-3 mx-2 rounded-lg transition-all duration-200 ${
                  isActive
                    ? "bg-wellness-600 text-white shadow-md"
                    : "text-wellness-100 hover:bg-wellness-700 hover:text-white"
                }`
              }
            >
              <Typography variant="body1" weight="medium">
                {name}
              </Typography>
            </NavLink>
          ))}
        </nav>
      </div>

      {/* Main Content */}
      <div className="flex-grow p-6 bg-gradient-to-br from-neutral-50 to-wellness-50 overflow-auto">
        <Routes>
          <Route path="registration" element={<Registration />} />
          <Route path="category" element={<Category />} />
          <Route path="grades" element={<Grades />} />
          <Route path="programs" element={<Programs />} />
          <Route path="programmanagement" element={<ProgramManagement />} />
          <Route path="attendancesheet" element={<AttendanceSheet />} />
        </Routes>
      </div>
    </div>
  );
};

export default Dashboard;
