import React from 'react';
import { cn } from '../../utils/cn';

export interface TypographyProps extends React.HTMLAttributes<HTMLElement> {
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'overline';
  color?: 'primary' | 'secondary' | 'accent' | 'neutral' | 'success' | 'warning' | 'error';
  align?: 'left' | 'center' | 'right' | 'justify';
  weight?: 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';
  as?: keyof JSX.IntrinsicElements;
  children: React.ReactNode;
}

const Typography = React.forwardRef<HTMLElement, TypographyProps>(
  ({
    className,
    variant = 'body1',
    color = 'neutral',
    align = 'left',
    weight,
    as,
    children,
    ...props
  }, ref) => {
    // Default element mapping
    const elementMap = {
      h1: 'h1',
      h2: 'h2',
      h3: 'h3',
      h4: 'h4',
      h5: 'h5',
      h6: 'h6',
      body1: 'p',
      body2: 'p',
      caption: 'span',
      overline: 'span',
    };
    
    const Component = as || elementMap[variant] || 'p';
    
    // Variant styles
    const variants = {
      h1: 'text-4xl md:text-5xl font-bold leading-tight',
      h2: 'text-3xl md:text-4xl font-bold leading-tight',
      h3: 'text-2xl md:text-3xl font-bold leading-snug',
      h4: 'text-xl md:text-2xl font-semibold leading-snug',
      h5: 'text-lg md:text-xl font-semibold leading-normal',
      h6: 'text-base md:text-lg font-semibold leading-normal',
      body1: 'text-base leading-relaxed',
      body2: 'text-sm leading-relaxed',
      caption: 'text-xs leading-normal',
      overline: 'text-xs uppercase tracking-wider font-medium leading-normal',
    };
    
    // Color styles
    const colors = {
      primary: 'text-secondary-700',
      secondary: 'text-secondary-600',
      accent: 'text-accent-600',
      neutral: 'text-neutral-700',
      success: 'text-green-600',
      warning: 'text-amber-600',
      error: 'text-red-600',
    };
    
    // Alignment styles
    const alignments = {
      left: 'text-left',
      center: 'text-center',
      right: 'text-right',
      justify: 'text-justify',
    };
    
    // Weight styles (only if explicitly provided)
    const weights = {
      normal: 'font-normal',
      medium: 'font-medium',
      semibold: 'font-semibold',
      bold: 'font-bold',
      extrabold: 'font-extrabold',
    };
    
    return (
      <Component
        ref={ref}
        className={cn(
          variants[variant],
          colors[color],
          alignments[align],
          weight && weights[weight],
          className
        )}
        {...props}
      >
        {children}
      </Component>
    );
  }
);

Typography.displayName = 'Typography';

// Convenience components
export const Heading1 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h1" {...props} />
);

export const Heading2 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h2" {...props} />
);

export const Heading3 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h3" {...props} />
);

export const Heading4 = React.forwardRef<HTMLHeadingElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="h4" {...props} />
);

export const Body1 = React.forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="body1" {...props} />
);

export const Body2 = React.forwardRef<HTMLParagraphElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="body2" {...props} />
);

export const Caption = React.forwardRef<HTMLSpanElement, Omit<TypographyProps, 'variant'>>(
  (props, ref) => <Typography ref={ref} variant="caption" {...props} />
);

export default Typography;
