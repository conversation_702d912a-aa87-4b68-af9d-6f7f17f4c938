import React from 'react';
import { cn } from '../../utils/cn';

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  variant?: 'default' | 'error';
  inputSize?: 'sm' | 'md' | 'lg';
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  label?: string;
  error?: string;
  helperText?: string;
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({
    className,
    variant = 'default',
    inputSize = 'md',
    leftIcon,
    rightIcon,
    label,
    error,
    helperText,
    type = 'text',
    id,
    ...props
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const hasError = error || variant === 'error';
    
    const baseStyles = 'w-full rounded-xl border transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-1 disabled:opacity-50 disabled:cursor-not-allowed';
    
    const variants = {
      default: 'border-neutral-300 focus:border-wellness-500 focus:ring-wellness-200 bg-white',
      error: 'border-red-300 focus:border-red-500 focus:ring-red-200 bg-red-50/50',
    };
    
    const sizes = {
      sm: 'px-3 py-2 text-sm',
      md: 'px-4 py-2.5 text-base',
      lg: 'px-5 py-3 text-lg',
    };
    
    const iconPadding = {
      sm: leftIcon ? 'pl-10' : rightIcon ? 'pr-10' : '',
      md: leftIcon ? 'pl-12' : rightIcon ? 'pr-12' : '',
      lg: leftIcon ? 'pl-14' : rightIcon ? 'pr-14' : '',
    };
    
    return (
      <div className="w-full">
        {label && (
          <label
            htmlFor={inputId}
            className="block text-sm font-medium text-neutral-700 mb-2"
          >
            {label}
          </label>
        )}
        
        <div className="relative">
          {leftIcon && (
            <div className={cn(
              'absolute left-0 top-0 h-full flex items-center justify-center text-neutral-400',
              inputSize === 'sm' ? 'w-10' : inputSize === 'md' ? 'w-12' : 'w-14'
            )}>
              {leftIcon}
            </div>
          )}
          
          <input
            ref={ref}
            id={inputId}
            type={type}
            className={cn(
              baseStyles,
              variants[hasError ? 'error' : 'default'],
              sizes[inputSize],
              iconPadding[inputSize],
              className
            )}
            {...props}
          />
          
          {rightIcon && (
            <div className={cn(
              'absolute right-0 top-0 h-full flex items-center justify-center text-neutral-400',
              inputSize === 'sm' ? 'w-10' : inputSize === 'md' ? 'w-12' : 'w-14'
            )}>
              {rightIcon}
            </div>
          )}
        </div>
        
        {(error || helperText) && (
          <p className={cn(
            'mt-2 text-sm',
            hasError ? 'text-red-600' : 'text-neutral-500'
          )}>
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);

Input.displayName = 'Input';

export default Input;
