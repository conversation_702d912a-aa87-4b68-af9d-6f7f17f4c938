import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, FiArrowRight } from 'react-icons/fi';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { BASE_URL } from '../config';
import { checkDelegate } from '../hooks/manageDelegates';
import { PageContent } from "./PageContentStyle";
import { <PERSON>ton, Card, CardHeader, CardTitle, CardDescription, CardContent, Input, Typography } from './ui';

const LoginBox: React.FC = () => {
  const [mobileNumber, setMobileNumber] = useState('');
  const [otp, setOtp] = useState(['', '', '', '']);
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [otpSent, setOtpSent] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();

  const generateOTP = (length = 4): string => Math.random().toString().slice(2, 2 + length);

const location = useLocation();
const programId = location.state?.programId || 'NA';
const fromQR = location.state?.fromQR || 'NA';

  const handleSendOtp = async () => {
    if (mobileNumber.length !== 10) {
      setError('Enter a valid 10-digit mobile number');
      return;
    }

    try {
      const generatedOtp = generateOTP();
      setOtpSent(generatedOtp);

      // const response = await axios.post(`${BASE_URL}/sendotp/`, {
      //   otp: generatedOtp,
      //   mobileNumber,
      // });

       setOtpSent("1234");

      //if (response.status === 200) {
       if (1===1) {
        setIsOtpSent(true);
        setError(null);
        alert('OTP sent to your mobile number');
      } else {
        throw new Error('Failed to send OTP');
      }
    } catch (err) {
      console.error('Error sending OTP:', err);
      setError('Failed to send OTP. Please try again.');
    }
  };

  const handleVerifyOtp = async () => {
    try {
      const enteredOtp = otp.join('');
      if (enteredOtp === otpSent) {
        alert('OTP validated successfully');
        
        // Await for the delegate check
        const delegateexist = await checkDelegate(mobileNumber);

        console.log(delegateexist);
        
        if (Number(delegateexist) > 0 && (fromQR== 'NA' || fromQR == false)) {
          navigate('/profile', {
            state: { delegateexist, programId, fromQR },
          });
        } else {
          navigate('/register', {
            state: { delegateexist, programId, fromQR },
          }); // Navigate to the registration page
        }
      } else {
        setError('Invalid OTP'); // Update error state for invalid OTP
      }
    } catch (error) {
      console.error('Error during OTP verification:', error);
      alert('Something went wrong. Please try again.');
    }
  };

  const handleOtpChange = (index: number, value: string) => {
    if (/^\d?$/.test(value)) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);

      if (value && index < 3) {
        document.getElementById(`otp-${index + 1}`)?.focus();
      }
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otp[index] && index > 0) {
      document.getElementById(`otp-${index - 1}`)?.focus();
    }
  };

  return (
    <PageContent>
      <div className="flex justify-center pt-4 max-w-md mx-auto">
        <Card variant="glass" className="w-full mt-6">
          <CardHeader>
            <CardTitle className="text-center">
              {isOtpSent ? 'Enter OTP' : 'Welcome!'}
            </CardTitle>
            <CardDescription className="text-center">
              {isOtpSent
                ? `We've sent an OTP to ${mobileNumber}`
                : 'Please validate your mobile number to proceed'}
            </CardDescription>
          </CardHeader>

          <CardContent className="space-y-4">
            {error && (
              <Typography variant="body2" color="error" align="center">
                {error}
              </Typography>
            )}

            {!isOtpSent ? (
              <Input
                type="tel"
                value={mobileNumber}
                onChange={(e) => setMobileNumber(e.target.value)}
                placeholder="Enter mobile number"
                maxLength={10}
                leftIcon={<FiPhone className="w-4 h-4" />}
                inputSize="lg"
              />
            ) : (
              <div className="flex space-x-2 justify-center">
                {otp.map((digit, index) => (
                  <input
                    key={index}
                    id={`otp-${index}`}
                    type="number"
                    maxLength={1}
                    value={digit}
                    onChange={(e) => handleOtpChange(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    className="w-12 h-12 text-center text-xl rounded-xl border border-neutral-300 focus:ring-2 focus:ring-wellness-500 focus:border-wellness-500 transition-all duration-200"
                  />
                ))}
              </div>
            )}

            <Button
              onClick={isOtpSent ? handleVerifyOtp : handleSendOtp}
              variant="primary"
              size="lg"
              fullWidth
              rightIcon={<FiArrowRight className="w-4 h-4" />}
            >
              {isOtpSent ? 'Verify OTP' : 'Send OTP'}
            </Button>

            {isOtpSent && (
              <Button
                onClick={() => setIsOtpSent(false)}
                variant="ghost"
                size="sm"
                fullWidth
              >
                Change mobile number
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </PageContent>
  );
};

export default LoginBox;
