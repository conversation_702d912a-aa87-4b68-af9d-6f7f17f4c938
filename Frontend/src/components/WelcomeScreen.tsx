import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import styled from 'styled-components';
import { PageContent } from "./PageContentStyle";
import { Typography } from './ui';

// PageContent styled component
// const PageContent = styled.div`
//   margin-top: 16px;
//   text-align: center;
//   font-size: 16px;
//   padding: 16px;
//   min-height: 100%; /* Make sure it fills available vertical space */
//   display: flex;
//   flex-direction: column;
//   justify-content: center; /* Centers the content vertically */
// `;

const Title = styled.h1`
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 1rem;
`;

const Description = styled.p`
  color: #4a4a4a;
  margin-bottom: 1rem;
`;

const SubDescription = styled.p`
  color: #9e9e9e;
  margin-bottom: 1.5rem;
`;

const Highlight = styled.h2`
  font-size: 1.25rem;
  font-weight: 600;
  color: #3b82f6;
`;

const CountdownText = styled.p`
  color: #6b7280;
  font-size: 0.875rem;
  margin-top: 1rem;
`;

const WelcomeScreen: React.FC = () => {
  const navigate = useNavigate();
  const [timeLeft, setTimeLeft] = useState(10); // Countdown starts at 10 seconds

  useEffect(() => {
    const interval = setInterval(() => {
      setTimeLeft((prev) => (prev > 0 ? prev - 1 : 0));
    }, 1000);

    const timer = setTimeout(() => {
      navigate("/mainpage"); // Redirect after 10 seconds
    }, 5000);

    return () => {
      clearInterval(interval); // Clean up interval
      clearTimeout(timer); // Clean up timeout
    };
  }, [navigate]);

  return (
    <PageContent>
      {/* <Title>Welcome to Wholistic Wellness</Title>
      <Description>
        To achieve health, harmony, and quality in every aspect of life — Body, Mind, Soul & Family.
      </Description>
      <SubDescription>Brought to you by</SubDescription>
      <Highlight>Bhaktivedanta Hospital and Research Centre</Highlight>
      <CountdownText>
        Redirecting in {timeLeft} seconds...
      </CountdownText> */}

      <Typography variant="h2" color="primary" align="center" className="mb-6">
        Welcome to Wholistic Wellness
      </Typography>
      <Typography variant="h5" color="neutral" align="center" className="mb-6">
        To achieve health, harmony, and quality in every aspect of life —{" "}
        <span className="font-bold text-wellness-700">Body, Mind, Soul & Family.</span>
      </Typography>
      <Typography variant="caption" color="neutral" align="center" className="mb-4">
        Brought to you by
      </Typography>
      <div className="flex justify-center mb-4">
        <img
          src="/BVH-Logo-unscreen.gif"
          alt="Bhaktivedanta Hospital Logo"
          className="w-64 h-auto"
        />
      </div>
      
    

    </PageContent>
  );
};

export default WelcomeScreen;
