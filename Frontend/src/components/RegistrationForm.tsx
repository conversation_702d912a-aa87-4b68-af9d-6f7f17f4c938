import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { BASE_URL } from '../config';

import useFetchGender from '../hooks/useFetchGender';
import useFetchMaritalstatus from '../hooks/useFetchMaritalstatus';
import useFetchOccupations from '../hooks/useFetchOccupations';
import useFetchAreas from '../hooks/useFetchAreas';
import { postDelegateDemo, updateDelegateDemo, checkDelegate } from '../hooks/manageDelegates';
import { <PERSON>ton, Card, CardHeader, CardTitle, CardContent, Input, Typography } from './ui';

interface RegistrationFormValues {
  id?: string;
  regname: string;
  mobileno: string;
  dateOfBirth: string;
  gender: number;
  maritalstatus: number;
  occupation: number;
  area: number;
  email: string;
}

const RegistrationForm: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const mobileInputRef = useRef<HTMLInputElement>(null);

  const { genders } = useFetchGender();
  const { maritalstatuses } = useFetchMaritalstatus();
  const { occupations } = useFetchOccupations();
  const { areas } = useFetchAreas();

  const [userFromAPI, setUserFromAPI] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>('');
  // const [selectedCards, setSelectedCards] = useState<string[]>([]);
  
  const delegateexist = location.state?.delegateexist;
  const fromQR = location.state?.fromQR;
  const programId = location.state?.programId;
  const profileData = location.state?.profileData;

  // useEffect(() => {
  //   if (programId) {
  //     setSelectedCards([courseId]); // Assuming courseId is a string
  //   }
  // }, [courseId]);

  // Fetch user data from API if delegateexist is available and no profileData
  useEffect(() => {
    const fetchData = async () => {
      if (delegateexist && !profileData) {
        setLoading(true);
        try {
          const response = await axios.post(`${BASE_URL}/delegates/getdelegatesdemo/`, {
            delegateid: delegateexist,
          });
          setUserFromAPI(response.data[0]);
        } catch (err: any) {
          setError("Failed to fetch delegate data.");
        } finally {
          setLoading(false);
        }
      }
    };

    fetchData();
  }, [delegateexist, profileData]);

  // Format date to yyyy-mm-dd
  const formatDate = (date: string) => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = (d.getMonth() + 1).toString().padStart(2, '0');
    const day = d.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Determine initial record source
  const sourceData = profileData || userFromAPI;

  const [formData, setFormData] = useState<RegistrationFormValues>({
    id: sourceData?.id || "",
    regname: sourceData?.regname || "",
    mobileno: sourceData?.mobileno || "",
    dateOfBirth: sourceData?.dateofbirth ? formatDate(sourceData.dateofbirth) : "",
    gender: sourceData?.genderid ? Number(sourceData.genderid) : 0,
    maritalstatus: sourceData?.maritalstatusid ? Number(sourceData.maritalstatusid) : 0,
    occupation: sourceData?.occupationid ? Number(sourceData.occupationid) : 0,
    area: sourceData?.areaid ? Number(sourceData.areaid) : 0,
    email: sourceData?.email || "<EMAIL>",
  });

  // Update form data once userFromAPI is loaded
  useEffect(() => {
    if (userFromAPI) {
      setFormData({
        id: userFromAPI?.id || "",
        regname: userFromAPI?.regname || "",
        mobileno: userFromAPI?.mobileno || "",
        dateOfBirth: userFromAPI?.dateofbirth ? formatDate(userFromAPI.dateofbirth) : "",
        gender: userFromAPI?.genderid ? Number(userFromAPI.genderid) : 0,
        maritalstatus: userFromAPI?.maritalstatusid ? Number(userFromAPI.maritalstatusid) : 0,
        occupation: userFromAPI?.occupationid ? Number(userFromAPI.occupationid) : 0,
        area: userFromAPI?.areaid ? Number(userFromAPI.areaid) : 0,
        email: userFromAPI?.email || "<EMAIL>",
      });
    }
  }, [userFromAPI]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: ["gender", "maritalstatus", "occupation", "area"].includes(name)
        ? Number(value)
        : value,
    }));
  };

  const handleMobilecheck = async () => {
    try {
      const exists = await checkDelegate(formData.mobileno);
      if (Number(exists) > 0 && formData.id === "") {
        alert('Mobile Number already registered.');
        mobileInputRef.current?.focus();
      }
    } catch {
      alert('Something went wrong during mobile validation.');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    try {
      if (formData.id) {
        await updateDelegateDemo(
          formData.regname,
          formData.email,
          formData.dateOfBirth,
          String(formData.gender),
          String(formData.maritalstatus),
          String(formData.occupation),
          formData.mobileno,
          String(formData.area)
        );
        alert("Record updated successfully!");
      } else {
        await postDelegateDemo(
          formData.regname,
          formData.email,
          formData.dateOfBirth,
          String(formData.gender),
          String(formData.maritalstatus),
          String(formData.occupation),
          formData.mobileno,
          String(formData.area)
        );
        alert("Record created successfully!");
      }

      // Reset form
      setFormData({
        id: "",
        regname: "",
        mobileno: "",
        dateOfBirth: "",
        gender: 0,
        maritalstatus: 0,
        occupation: 0,
        area: 0,
        email: "",
      });

      if (fromQR== 'NA' || fromQR == false) {
          navigate("/courseselection");
        } 
        else {
          navigate('/profileqr', {
            state: { delegateexist, programId, fromQR },
          }); // Navigate to the registration page
        }

      // navigate("/courseselection");
    } catch (err) {
      console.error("Submission error:", err);
      alert("An error occurred during submission.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loading) return <div className="text-center mt-10">Loading delegate information...</div>;
  if (error) return <div className="text-center text-red-500 mt-10">{error}</div>;

  return (
    <div className="flex items-center justify-center min-h-screen bg-gradient-wellness">
      <Card variant="elevated" className="w-full max-w-md m-4">
        <CardHeader>
          <CardTitle className="text-center">
            {formData.id ? "Verify Registration Details" : "Registration Form"}
          </CardTitle>
          <Typography variant="body2" color="neutral" align="center">
            {formData.id ? "Please verify your details below" : "Enter your details to proceed with registration"}
          </Typography>
        </CardHeader>

        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">

            {/* Name */}
            <Input
              label="Name"
              type="text"
              name="regname"
              value={formData.regname}
              onChange={handleChange}
              required
            />

            {/* Mobile */}
            <Input
              ref={mobileInputRef}
              label="Mobile Number"
              type="tel"
              name="mobileno"
              value={formData.mobileno}
              onChange={handleChange}
              onBlur={handleMobilecheck}
              required
              maxLength={10}
            />

            {/* Date of Birth */}
            <Input
              label="Date of Birth"
              type="date"
              name="dateOfBirth"
              value={formData.dateOfBirth}
              onChange={handleChange}
              required
            />

            {/* Gender */}
            <div>
              <Typography variant="body2" weight="medium" className="mb-3">Gender</Typography>
              <div className="flex flex-wrap gap-3">
                {genders.map((g) => (
                  <label key={g.id} className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="gender"
                      value={g.id}
                      checked={formData.gender === g.id}
                      onChange={handleChange}
                      className="w-4 h-4 text-wellness-600 border-neutral-300 focus:ring-wellness-500"
                    />
                    <Typography variant="body2" className="ml-2">{g.gender}</Typography>
                  </label>
                ))}
              </div>
            </div>

            {/* Marital Status */}
            <div>
              <Typography variant="body2" weight="medium" className="mb-3">Marital Status</Typography>
              <div className="flex flex-wrap gap-3">
                {maritalstatuses.map((ms) => (
                  <label key={ms.id} className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="maritalstatus"
                      value={ms.id}
                      checked={formData.maritalstatus === ms.id}
                      onChange={handleChange}
                      className="w-4 h-4 text-wellness-600 border-neutral-300 focus:ring-wellness-500"
                    />
                    <Typography variant="body2" className="ml-2">{ms.maritalstatus}</Typography>
                  </label>
                ))}
              </div>
            </div>

            {/* Occupation */}
            <div>
              <Typography variant="body2" weight="medium" className="mb-3">Occupation</Typography>
              <div className="flex flex-wrap gap-3">
                {occupations.map((o) => (
                  <label key={o.id} className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="occupation"
                      value={o.id}
                      checked={formData.occupation === o.id}
                      onChange={handleChange}
                      className="w-4 h-4 text-wellness-600 border-neutral-300 focus:ring-wellness-500"
                    />
                    <Typography variant="body2" className="ml-2">{o.occupation}</Typography>
                  </label>
                ))}
              </div>
            </div>

            {/* Area */}
            <div>
              <Typography variant="body2" weight="medium" className="mb-3">Area</Typography>
              <div className="flex flex-wrap gap-3">
                {areas.map((a) => (
                  <label key={a.id} className="flex items-center cursor-pointer">
                    <input
                      type="radio"
                      name="area"
                      value={a.id}
                      checked={formData.area === a.id}
                      onChange={handleChange}
                      className="w-4 h-4 text-wellness-600 border-neutral-300 focus:ring-wellness-500"
                    />
                    <Typography variant="body2" className="ml-2">{a.area}</Typography>
                  </label>
                ))}
              </div>
            </div>

            <Button
              type="submit"
              variant="primary"
              size="lg"
              fullWidth
              isLoading={isSubmitting}
            >
              {formData.id ? "Update Details" : "Register"}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default RegistrationForm;
