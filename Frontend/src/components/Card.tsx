import React from 'react';
import { Card as UICard, CardContent, Typography } from './ui';

interface CardProps {
  title: string;
  subtitle?: string;
  showIcons?: boolean;
}

const Card = ({ title, subtitle }: CardProps) => {
  return (
    <UICard
      variant="glass"
      hover
      className="w-full max-w-4xl mx-auto"
    >
      <CardContent className="text-center p-6 md:p-8">
        <Typography
          variant="h2"
          className="bg-gradient-to-r from-secondary-600 to-secondary-800 bg-clip-text text-transparent mb-4 md:text-4xl lg:text-5xl"
        >
          {title}
        </Typography>
        {subtitle && (
          <Typography variant="body1" color="neutral" className="opacity-80 md:text-lg">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </UICard>
  );
};

export default Card;