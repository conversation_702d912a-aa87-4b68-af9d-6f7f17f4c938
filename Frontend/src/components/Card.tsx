import React from 'react';
import { Card as UICard, CardContent, Typography } from './ui';

interface CardProps {
  title: string;
  subtitle?: string;
  showIcons?: boolean;
}

const Card = ({ title, subtitle }: CardProps) => {
  return (
    <UICard
      variant="glass"
      hover
      className="w-full max-w-[90vw] sm:max-w-lg mx-auto"
    >
      <CardContent className="text-center">
        <Typography
          variant="h3"
          className="bg-gradient-to-r from-wellness-600 to-wellness-800 bg-clip-text text-transparent mb-2"
        >
          {title}
        </Typography>
        {subtitle && (
          <Typography variant="body2" color="neutral" className="opacity-80">
            {subtitle}
          </Typography>
        )}
      </CardContent>
    </UICard>
  );
};

export default Card;