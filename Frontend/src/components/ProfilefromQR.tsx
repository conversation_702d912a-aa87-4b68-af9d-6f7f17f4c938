import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from 'react-router-dom';
import axios from 'axios';
import { BASE_URL } from '../config';
import ProfileCardQR from "./ProfileCardQR";
import { getProgramByIdDemo } from "../hooks/managePrograms";
import { Typography } from './ui';

interface Course {
  coursecategory: string;
  title: string;
  image: string;
}

interface User {
  agerange: string;
  area: string;
  gender: string;
  id: number;
  maritalstatus: string;
  mobileno: string;
  occupation: string;
  registrationdate: string;
  regname: string;
  dateofbirth: string;
  genderid: string;
  maritalstatusid: string;
  occupationid: string;
  areaid: string;
  grade: string;
}

const ProfilefromQR: React.FC = () => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string>('');
  const [delegateprogram, setDelegateProgram] = useState<Course[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [delegateexist, setDelegateExist] = useState<string | null | undefined>(undefined);

  const navigate = useNavigate();
  const location = useLocation();

  // Pulls these values from location.state
  const fromQR = location.state?.fromQR;
  const programId = location.state?.programId;
  const initialDelegateExist = location.state?.delegateexist ?? null;
  const delegateId = localStorage.getItem("delegateId") || "";

  // Step 1: Ensure delegateexist is set (from state or localStorage)
 useEffect(() => {
  // Try to find delegateexist on mount
  if (typeof delegateexist === "undefined") {
    const stateVal = location.state?.delegateexist ?? null;
    if (stateVal) {
      setDelegateExist(stateVal);
    } else {
      const stored = localStorage.getItem("delegateId");
      if (stored) {
        setDelegateExist(stored);
      } else {
        setDelegateExist(null); // We have checked and not found
      }
    }
  }
}, [delegateexist, location.state]);

useEffect(() => {
  // Only run if we've checked for delegateexist, and have a value, and have programId
  if (typeof delegateexist === "undefined" || !programId) return;
  if (delegateexist === null) {
    setLoading(false);
    setError("Invalid delegate.");
    return;
  }

  // Only now, safe to fetch
  const fetchData = async () => {
    setLoading(true);
    try {
      const [userDataResponse, programResponse] = await Promise.all([
        axios.post(`${BASE_URL}/delegates/getdelegatesdemo/`, {
          delegateid: delegateexist,
        }),
        axios.post(`${BASE_URL}/programs/searchprogrambyid`, {
          programId: programId,
        }),
      ]);
      // ...rest as before
      const user = Array.isArray(userDataResponse.data) && userDataResponse.data.length > 0?
      userDataResponse.data[0]: userDataResponse.data;

      setUser(user);

      let programs: Course[] = [];

      if (Array.isArray(programResponse.data)) {
        programs = programResponse.data; 
      } else if (programResponse.data && typeof programResponse.data === "object") {
          programs = [programResponse.data];
      }

      setDelegateProgram(programs);
    

      setError("");
    } catch (err: any) {
      setError(err.message || "An error occurred while fetching data.");
    } finally {
      setLoading(false);
    }
  };
  fetchData();
}, [delegateexist, programId, BASE_URL]);

  // Edit handler
  const handleEdit = () => {
    if (user) {
      const profileData = {
        id: user.id,
        registrationDate: user.registrationdate,
        regname: user.regname,
        ageRange: user.agerange,
        mobileno: user.mobileno,
        gender: user.gender,
        maritalStatus: user.maritalstatus,
        occupation: user.occupation,
        area: user.area,
        program: delegateprogram,
        dateofbirth: user.dateofbirth,
        genderid: user.genderid,
        maritalstatusid: user.maritalstatusid,
        occupationid: user.occupationid,
        areaid: user.areaid,
        grade: user.grade,
      };

      navigate('/register', { state: { profileData } });
      setIsEditing(true);
    }
  };

  // UI
  if (loading) return (
    <div className="min-h-screen flex items-center justify-center">
      <Typography variant="body1" color="neutral">Loading...</Typography>
    </div>
  );
  if (error) return (
    <div className="min-h-screen flex items-center justify-center">
      <Typography variant="body1" color="error">Error: {error}</Typography>
    </div>
  );

  const profileData = {
    id: user?.id,
    registrationDate: user?.registrationdate,
    regname: user?.regname,
    ageRange: user?.agerange,
    mobileno: user?.mobileno,
    gender: user?.gender,
    maritalStatus: user?.maritalstatus,
    occupation: user?.occupation,
    area: user?.area,
    program: delegateprogram,
    dateofbirth: user?.dateofbirth,
    genderid: user?.genderid,
    maritalstatusid: user?.maritalstatusid,
    occupationid: user?.occupationid,
    areaid: user?.areaid,
    grade: user?.grade,
  };

  return (
    <ProfileCardQR
      {...profileData}
      onEdit={handleEdit}
    />
  );
};

export default ProfilefromQR;
