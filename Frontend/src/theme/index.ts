// Theme configuration for Wholistic Wellness App
export const theme = {
  colors: {
    // Primary wellness colors
    primary: {
      50: '#f8fdf4',
      100: '#f0fae6',
      200: '#dff4cc',
      300: '#c8e9a3',
      400: '#aed975',
      500: '#94c74f',
      600: '#7bb03a',
      700: '#5f8a2f',
      800: '#4d6e29',
      900: '#415c26',
      950: '#213311',
    },
    // Secondary accent colors
    accent: {
      50: '#fefce8',
      100: '#fef9c3',
      200: '#fef08a',
      300: '#fde047',
      400: '#facc15',
      500: '#eab308',
      600: '#ca8a04',
      700: '#a16207',
      800: '#854d0e',
      900: '#713f12',
      950: '#422006',
    },
    // Neutral grays
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
      950: '#0a0a0a',
    },
    // Status colors
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  gradients: {
    primary: 'linear-gradient(135deg, #f8fdf4 0%, #dff4cc 50%, #aed975 100%)',
    accent: 'linear-gradient(135deg, #fefce8 0%, #fef08a 50%, #facc15 100%)',
    wellness: 'linear-gradient(135deg, #94c74f 0%, #7bb03a 50%, #5f8a2f 100%)',
    card: 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(255,255,255,0.7) 100%)',
  },
  
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      display: ['Inter', 'system-ui', 'sans-serif'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
  },
  
  spacing: {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
  },
  
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px',
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    wellness: '0 4px 6px -1px rgba(148, 199, 79, 0.1), 0 2px 4px -1px rgba(148, 199, 79, 0.06)',
    accent: '0 4px 6px -1px rgba(250, 204, 21, 0.1), 0 2px 4px -1px rgba(250, 204, 21, 0.06)',
  },
  
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  
  animations: {
    transition: {
      fast: '150ms ease-in-out',
      normal: '300ms ease-in-out',
      slow: '500ms ease-in-out',
    },
  },
} as const;

// Component-specific theme configurations
export const componentThemes = {
  button: {
    primary: 'bg-wellness-600 hover:bg-wellness-700 text-white shadow-wellness',
    secondary: 'bg-white hover:bg-neutral-50 text-wellness-700 border border-wellness-300',
    accent: 'bg-accent-500 hover:bg-accent-600 text-white shadow-accent',
    ghost: 'bg-transparent hover:bg-wellness-50 text-wellness-700',
    danger: 'bg-red-600 hover:bg-red-700 text-white',
  },
  
  card: {
    default: 'bg-white/90 backdrop-blur-sm border border-white/20 shadow-lg',
    elevated: 'bg-white shadow-xl border border-neutral-200',
    wellness: 'bg-gradient-to-br from-wellness-50 to-wellness-100 border border-wellness-200',
    accent: 'bg-gradient-to-br from-accent-50 to-accent-100 border border-accent-200',
  },
  
  input: {
    default: 'border border-neutral-300 focus:border-wellness-500 focus:ring-2 focus:ring-wellness-200',
    error: 'border border-red-300 focus:border-red-500 focus:ring-2 focus:ring-red-200',
  },
  
  text: {
    heading: 'text-neutral-900 font-bold',
    subheading: 'text-neutral-700 font-semibold',
    body: 'text-neutral-600',
    caption: 'text-neutral-500 text-sm',
    accent: 'text-wellness-700 font-medium',
  },
} as const;

export type Theme = typeof theme;
export type ComponentThemes = typeof componentThemes;
