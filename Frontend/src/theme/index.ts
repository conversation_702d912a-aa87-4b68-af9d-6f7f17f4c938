// Theme configuration for Wholistic Wellness App
export const theme = {
  colors: {
    // Primary colors - White theme
    primary: {
      50: '#ffffff',
      100: '#fefefe',
      200: '#fafafa',
      300: '#f5f5f5',
      400: '#f0f0f0',
      500: '#ffffff',
      600: '#f8f9fa',
      700: '#e9ecef',
      800: '#dee2e6',
      900: '#ced4da',
      950: '#adb5bd',
    },
    // Secondary colors - Dark Blue
    secondary: {
      50: '#eff6ff',
      100: '#dbeafe',
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#1e40af',
      700: '#1e3a8a',
      800: '#1e293b',
      900: '#0f172a',
      950: '#020617',
    },
    // Accent colors - Light blue
    accent: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      200: '#bae6fd',
      300: '#7dd3fc',
      400: '#38bdf8',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
      800: '#075985',
      900: '#0c4a6e',
      950: '#082f49',
    },
    // Neutral grays
    neutral: {
      50: '#fafafa',
      100: '#f5f5f5',
      200: '#e5e5e5',
      300: '#d4d4d4',
      400: '#a3a3a3',
      500: '#737373',
      600: '#525252',
      700: '#404040',
      800: '#262626',
      900: '#171717',
      950: '#0a0a0a',
    },
    // Status colors
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
  },
  
  gradients: {
    primary: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%)',
    secondary: 'linear-gradient(135deg, #1e40af 0%, #1e3a8a 50%, #1e293b 100%)',
    accent: 'linear-gradient(135deg, #f0f9ff 0%, #bae6fd 50%, #0ea5e9 100%)',
    card: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.85) 100%)',
  },
  
  typography: {
    fontFamily: {
      sans: ['Inter', 'system-ui', 'sans-serif'],
      display: ['Inter', 'system-ui', 'sans-serif'],
    },
    fontSize: {
      xs: '0.75rem',
      sm: '0.875rem',
      base: '1rem',
      lg: '1.125rem',
      xl: '1.25rem',
      '2xl': '1.5rem',
      '3xl': '1.875rem',
      '4xl': '2.25rem',
      '5xl': '3rem',
    },
    fontWeight: {
      normal: '400',
      medium: '500',
      semibold: '600',
      bold: '700',
      extrabold: '800',
    },
  },
  
  spacing: {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
    '3xl': '4rem',
  },
  
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    '3xl': '1.5rem',
    full: '9999px',
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
    xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
    wellness: '0 4px 6px -1px rgba(148, 199, 79, 0.1), 0 2px 4px -1px rgba(148, 199, 79, 0.06)',
    accent: '0 4px 6px -1px rgba(250, 204, 21, 0.1), 0 2px 4px -1px rgba(250, 204, 21, 0.06)',
  },
  
  breakpoints: {
    sm: '640px',
    md: '768px',
    lg: '1024px',
    xl: '1280px',
    '2xl': '1536px',
  },
  
  animations: {
    transition: {
      fast: '150ms ease-in-out',
      normal: '300ms ease-in-out',
      slow: '500ms ease-in-out',
    },
  },
} as const;

// Component-specific theme configurations
export const componentThemes = {
  button: {
    primary: 'bg-secondary-600 hover:bg-secondary-700 text-white shadow-secondary',
    secondary: 'bg-white hover:bg-neutral-50 text-secondary-700 border border-secondary-300',
    accent: 'bg-accent-500 hover:bg-accent-600 text-white shadow-accent',
    ghost: 'bg-transparent hover:bg-neutral-50 text-secondary-700',
    danger: 'bg-red-600 hover:bg-red-700 text-white',
  },
  
  card: {
    default: 'bg-white/95 backdrop-blur-sm border border-neutral-200 shadow-lg',
    elevated: 'bg-white shadow-xl border border-neutral-200',
    primary: 'bg-gradient-to-br from-primary-50 to-primary-100 border border-neutral-200',
    accent: 'bg-gradient-to-br from-accent-50 to-accent-100 border border-accent-200',
  },

  input: {
    default: 'border border-neutral-300 focus:border-secondary-500 focus:ring-2 focus:ring-secondary-200',
    error: 'border border-red-300 focus:border-red-500 focus:ring-2 focus:ring-red-200',
  },

  text: {
    heading: 'text-neutral-900 font-bold',
    subheading: 'text-neutral-700 font-semibold',
    body: 'text-neutral-600',
    caption: 'text-neutral-500 text-sm',
    accent: 'text-secondary-700 font-medium',
  },
} as const;

export type Theme = typeof theme;
export type ComponentThemes = typeof componentThemes;
