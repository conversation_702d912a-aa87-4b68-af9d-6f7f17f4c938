# Wholistic Wellness Design System

## Overview
This design system provides a unified, consistent visual language for the Wholistic Wellness application. It includes color palettes, typography, spacing, components, and interaction patterns that reflect the wellness and health-focused nature of the application.

## Color Palette

### Primary Colors (Wellness Theme)
- **wellness-50**: `#f8fdf4` - Lightest green, for backgrounds
- **wellness-100**: `#f0fae6` - Very light green
- **wellness-200**: `#dff4cc` - Light green
- **wellness-300**: `#c8e9a3` - Medium light green
- **wellness-400**: `#aed975` - Medium green
- **wellness-500**: `#94c74f` - Base green
- **wellness-600**: `#7bb03a` - Primary green (main brand color)
- **wellness-700**: `#5f8a2f` - Dark green
- **wellness-800**: `#4d6e29` - Darker green
- **wellness-900**: `#415c26` - Very dark green
- **wellness-950**: `#213311` - Darkest green

### Accent Colors (Golden/Yellow)
- **accent-50**: `#fefce8` - Lightest yellow
- **accent-100**: `#fef9c3` - Very light yellow
- **accent-200**: `#fef08a` - Light yellow
- **accent-300**: `#fde047` - Medium light yellow
- **accent-400**: `#facc15` - Medium yellow
- **accent-500**: `#eab308` - Base yellow
- **accent-600**: `#ca8a04` - Primary yellow
- **accent-700**: `#a16207` - Dark yellow
- **accent-800**: `#854d0e` - Darker yellow
- **accent-900**: `#713f12` - Very dark yellow
- **accent-950**: `#422006` - Darkest yellow

### Neutral Colors
- **neutral-50** to **neutral-950**: Grayscale palette for text, borders, and backgrounds

## Typography

### Font Family
- **Primary**: Inter (Google Fonts)
- **Fallback**: system-ui, sans-serif

### Font Sizes
- **xs**: 0.75rem (12px)
- **sm**: 0.875rem (14px)
- **base**: 1rem (16px)
- **lg**: 1.125rem (18px)
- **xl**: 1.25rem (20px)
- **2xl**: 1.5rem (24px)
- **3xl**: 1.875rem (30px)
- **4xl**: 2.25rem (36px)
- **5xl**: 3rem (48px)

### Font Weights
- **normal**: 400
- **medium**: 500
- **semibold**: 600
- **bold**: 700
- **extrabold**: 800

## Components

### Button Variants
- **primary**: Main wellness green background
- **secondary**: White background with wellness border
- **accent**: Golden/yellow background
- **ghost**: Transparent background
- **danger**: Red background for destructive actions

### Card Variants
- **default**: White background with subtle border
- **elevated**: White background with stronger shadow
- **wellness**: Wellness gradient background
- **accent**: Accent gradient background
- **glass**: Semi-transparent with backdrop blur

### Input Styles
- **default**: Standard input with wellness focus colors
- **error**: Red border and background tint for errors

## Spacing Scale
- **xs**: 0.5rem (8px)
- **sm**: 0.75rem (12px)
- **md**: 1rem (16px)
- **lg**: 1.5rem (24px)
- **xl**: 2rem (32px)
- **2xl**: 3rem (48px)
- **3xl**: 4rem (64px)

## Border Radius
- **sm**: 0.25rem (4px)
- **md**: 0.375rem (6px)
- **lg**: 0.5rem (8px)
- **xl**: 0.75rem (12px)
- **2xl**: 1rem (16px)
- **3xl**: 1.5rem (24px)
- **full**: 9999px (circular)

## Shadows
- **wellness**: Custom shadow with wellness color tint
- **accent**: Custom shadow with accent color tint
- **sm**, **md**, **lg**, **xl**: Standard shadow scales

## Usage Guidelines

### Color Usage
1. Use **wellness-600** as the primary brand color
2. Use **accent-600** for secondary actions and highlights
3. Use neutral colors for text and backgrounds
4. Maintain sufficient contrast ratios for accessibility

### Typography Hierarchy
1. Use **h1-h6** variants for headings
2. Use **body1** for main content
3. Use **body2** for secondary content
4. Use **caption** for small text and labels

### Component Composition
```tsx
import { Button, Card, Typography } from './ui';

// Example usage
<Card variant="wellness">
  <Typography variant="h3" color="primary">
    Welcome to Wellness
  </Typography>
  <Button variant="primary" size="lg">
    Get Started
  </Button>
</Card>
```

## Accessibility

### Focus Management
- All interactive elements have visible focus indicators
- Focus rings use wellness colors for consistency
- Tab order follows logical content flow

### Color Contrast
- All text meets WCAG AA contrast requirements
- Interactive elements have sufficient contrast in all states

### Screen Reader Support
- Semantic HTML elements are used throughout
- ARIA labels and descriptions are provided where needed

## Animation Guidelines

### Timing
- **fast**: 150ms for micro-interactions
- **normal**: 300ms for standard transitions
- **slow**: 500ms for complex animations

### Easing
- Use `ease-in-out` for most transitions
- Use `ease-out` for entrance animations
- Use `ease-in` for exit animations

### Motion Principles
- Respect `prefers-reduced-motion` settings
- Keep animations subtle and purposeful
- Use consistent timing across similar interactions
