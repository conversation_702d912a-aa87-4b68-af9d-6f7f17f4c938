@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-neutral-200;
  }

  html, body {
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }

  body {
    @apply bg-gradient-primary font-sans text-neutral-900 antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  /* Custom scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.secondary.300') theme('colors.neutral.50');
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-neutral-50;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-secondary-300 rounded-full;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-secondary-400;
  }

  /* Focus styles */
  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-secondary-500 focus:ring-offset-2;
  }

  /* Glass morphism effect */
  .glass {
    @apply bg-white/90 backdrop-blur-sm border border-neutral-200;
  }

  /* Gradient text */
  .gradient-text-primary {
    @apply bg-gradient-to-r from-secondary-600 to-secondary-800 bg-clip-text text-transparent;
  }

  .gradient-text-accent {
    @apply bg-gradient-to-r from-accent-600 to-accent-800 bg-clip-text text-transparent;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
  }

  /* Button base styles */
  .btn-base {
    @apply inline-flex items-center justify-center font-medium rounded-xl transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }
}

@layer utilities {
  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fade-in 0.5s ease-out;
  }

  .animate-slide-up {
    animation: slide-up 0.3s ease-out;
  }

  /* Custom spacing */
  .space-y-6 > :not([hidden]) ~ :not([hidden]) {
    margin-top: 1.5rem;
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes fade-in {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  0% {
    transform: translateY(100%);
  }
  100% {
    transform: translateY(0);
  }
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

/* Custom radio button styles */
input[type="radio"] {
  @apply appearance-none w-4 h-4 border-2 border-neutral-300 rounded-full focus:ring-2 focus:ring-blue-200 transition-all duration-200;
}

input[type="radio"]:checked {
  border-color: #1e40af;
  background-color: #1e40af;
  background-image: radial-gradient(circle, white 30%, transparent 30%);
}

/* Custom checkbox styles */
input[type="checkbox"] {
  @apply appearance-none w-4 h-4 border-2 border-neutral-300 rounded focus:ring-2 focus:ring-blue-200 transition-all duration-200;
}

input[type="checkbox"]:checked {
  border-color: #1e40af;
  background-color: #1e40af;
  background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}