{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_pluginTransformFlowStripTypes", "_normalizeOptions", "_default", "exports", "default", "declarePreset", "api", "opts", "assertVersion", "all", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ignoreExtensions", "experimental_useHermesParser", "useHermesParser", "normalizeOptions", "plugins", "transformFlowStripTypes", "Number", "parseInt", "process", "versions", "node", "Error", "unshift", "overrides", "test", "filename"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declarePreset } from \"@babel/helper-plugin-utils\";\nimport transformFlowStripTypes from \"@babel/plugin-transform-flow-strip-types\";\nimport normalizeOptions from \"./normalize-options.ts\";\n\nexport default declarePreset((api, opts) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n  const {\n    all,\n    allowDeclareFields,\n    ignoreExtensions = process.env.BABEL_8_BREAKING ? false : true,\n    experimental_useHermesParser: useHermesParser = false,\n  } = normalizeOptions(opts);\n\n  const plugins: any[] = [\n    [transformFlowStripTypes, { all, allowDeclareFields }],\n  ];\n\n  if (useHermesParser) {\n    if (Number.parseInt(process.versions.node, 10) < 12) {\n      throw new Error(\n        \"The Hermes parser is only supported in Node 12 and later.\",\n      );\n    }\n    if (IS_STANDALONE) {\n      throw new Error(\n        \"The Hermes parser is not supported in the @babel/standalone.\",\n      );\n    }\n    plugins.unshift(\"babel-plugin-syntax-hermes-parser\");\n  }\n\n  if (ignoreExtensions) {\n    return { plugins };\n  }\n\n  return {\n    overrides: [\n      {\n        test: filename => filename == null || !/\\.tsx?$/.test(filename),\n        plugins,\n      },\n    ],\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,8BAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAF,OAAA;AAAsD,IAAAG,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEvC,IAAAC,gCAAa,EAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;EAC1CD,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC;EACtC,MAAM;IACJC,GAAG;IACHC,kBAAkB;IAClBC,gBAAgB,GAA0C,IAAI;IAC9DC,4BAA4B,EAAEC,eAAe,GAAG;EAClD,CAAC,GAAG,IAAAC,yBAAgB,EAACP,IAAI,CAAC;EAE1B,MAAMQ,OAAc,GAAG,CACrB,CAACC,sCAAuB,EAAE;IAAEP,GAAG;IAAEC;EAAmB,CAAC,CAAC,CACvD;EAED,IAAIG,eAAe,EAAE;IACnB,IAAII,MAAM,CAACC,QAAQ,CAACC,OAAO,CAACC,QAAQ,CAACC,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;MACnD,MAAM,IAAIC,KAAK,CACb,2DACF,CAAC;IACH;IAAC;IAMDP,OAAO,CAACQ,OAAO,CAAC,mCAAmC,CAAC;EACtD;EAEA,IAAIZ,gBAAgB,EAAE;IACpB,OAAO;MAAEI;IAAQ,CAAC;EACpB;EAEA,OAAO;IACLS,SAAS,EAAE,CACT;MACEC,IAAI,EAAEC,QAAQ,IAAIA,QAAQ,IAAI,IAAI,IAAI,CAAC,SAAS,CAACD,IAAI,CAACC,QAAQ,CAAC;MAC/DX;IACF,CAAC;EAEL,CAAC;AACH,CAAC,CAAC", "ignoreList": []}