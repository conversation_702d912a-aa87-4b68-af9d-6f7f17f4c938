{"name": "@babel/preset-flow", "version": "7.25.9", "description": "Babel preset for all Flow plugins.", "author": "The Babel Team (https://babel.dev/team)", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-preset-flow"}, "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-preset", "flowtype", "flow", "types"], "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "@babel/plugin-transform-flow-strip-types": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9", "babel-plugin-syntax-hermes-parser": "^0.19.1"}, "homepage": "https://babel.dev/docs/en/next/babel-preset-flow", "bugs": "https://github.com/babel/babel/issues?utf8=%E2%9C%93&q=is%3Aissue+label%3A%22area%3A%20flow%22+is%3Aopen", "engines": {"node": ">=6.9.0"}, "type": "commonjs"}