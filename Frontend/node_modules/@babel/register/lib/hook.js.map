{"version": 3, "names": ["addHook", "require", "sourceMapSupport", "piratesRevert", "maps", "Object", "create", "installSourceMapSupport", "install", "handleUncaughtExceptions", "environment", "retrieveSourceMap", "filename", "map", "url", "<PERSON><PERSON><PERSON>", "compiling", "internalModuleCache", "_cache", "compileBabel7", "client", "code", "isLocalClient", "compile", "globalModuleCache", "inputCode", "result", "transform", "exports", "register", "opts", "_opts$extensions", "bind", "exts", "extensions", "getDefaultExtensions", "ignoreNodeModules", "setOptions", "revert"], "sources": ["../src/hook.js"], "sourcesContent": ["\"use strict\";\n\nconst { addHook } = require(\"pirates\");\nconst sourceMapSupport = process.env.BABEL_8_BREAKING\n  ? require(\"@cspotcode/source-map-support\")\n  : require(\"source-map-support\");\n\nlet piratesRevert;\nconst maps = Object.create(null);\n\nfunction installSourceMapSupport() {\n  installSourceMapSupport = () => {}; // eslint-disable-line no-func-assign\n\n  sourceMapSupport.install({\n    handleUncaughtExceptions: false,\n    environment: \"node\",\n    retrieveSourceMap(filename) {\n      const map = maps?.[filename];\n      if (map) {\n        return { url: null, map: map };\n      } else {\n        return null;\n      }\n    },\n  });\n}\n\nif (!process.env.BABEL_8_BREAKING) {\n  // Babel 7 compiles files in the same thread where it hooks `require()`,\n  // so we must prevent mixing Babel plugin dependencies with the files\n  // to be compiled.\n  // All the `!process.env.BABEL_8_BREAKING` code in this file is for\n  // this purpose.\n\n  const Module = require(\"module\");\n\n  let compiling = false;\n  const internalModuleCache = Module._cache;\n\n  // eslint-disable-next-line no-var\n  var compileBabel7 = function compileBabel7(client, code, filename) {\n    if (!client.isLocalClient) return compile(client, code, filename);\n\n    if (compiling) return code;\n\n    const globalModuleCache = Module._cache;\n    try {\n      compiling = true;\n      Module._cache = internalModuleCache;\n      return compile(client, code, filename);\n    } finally {\n      compiling = false;\n      Module._cache = globalModuleCache;\n    }\n  };\n}\n\nfunction compile(client, inputCode, filename) {\n  const result = client.transform(inputCode, filename);\n\n  if (result === null) return inputCode;\n\n  const { code, map } = result;\n  if (map) {\n    maps[filename] = map;\n    installSourceMapSupport();\n  }\n  return code;\n}\n\nexports.register = function register(client, opts = {}) {\n  if (piratesRevert) piratesRevert();\n\n  piratesRevert = addHook(\n    (process.env.BABEL_8_BREAKING ? compile : compileBabel7).bind(null, client),\n    {\n      exts: opts.extensions ?? client.getDefaultExtensions(),\n      ignoreNodeModules: false,\n    },\n  );\n\n  client.setOptions(opts);\n};\n\nexports.revert = function revert() {\n  if (piratesRevert) piratesRevert();\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAM;EAAEA;AAAQ,CAAC,GAAGC,OAAO,CAAC,SAAS,CAAC;AACtC,MAAMC,gBAAgB,GAElBD,OAAO,CAAC,oBAAoB,CAAC;AAEjC,IAAIE,aAAa;AACjB,MAAMC,IAAI,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;AAEhC,SAASC,uBAAuBA,CAAA,EAAG;EACjCA,uBAAuB,GAAGA,CAAA,KAAM,CAAC,CAAC;EAElCL,gBAAgB,CAACM,OAAO,CAAC;IACvBC,wBAAwB,EAAE,KAAK;IAC/BC,WAAW,EAAE,MAAM;IACnBC,iBAAiBA,CAACC,QAAQ,EAAE;MAC1B,MAAMC,GAAG,GAAGT,IAAI,oBAAJA,IAAI,CAAGQ,QAAQ,CAAC;MAC5B,IAAIC,GAAG,EAAE;QACP,OAAO;UAAEC,GAAG,EAAE,IAAI;UAAED,GAAG,EAAEA;QAAI,CAAC;MAChC,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;EACF,CAAC,CAAC;AACJ;AAEmC;EAOjC,MAAME,MAAM,GAAGd,OAAO,CAAC,QAAQ,CAAC;EAEhC,IAAIe,SAAS,GAAG,KAAK;EACrB,MAAMC,mBAAmB,GAAGF,MAAM,CAACG,MAAM;EAGzC,IAAIC,aAAa,GAAG,SAASA,aAAaA,CAACC,MAAM,EAAEC,IAAI,EAAET,QAAQ,EAAE;IACjE,IAAI,CAACQ,MAAM,CAACE,aAAa,EAAE,OAAOC,OAAO,CAACH,MAAM,EAAEC,IAAI,EAAET,QAAQ,CAAC;IAEjE,IAAII,SAAS,EAAE,OAAOK,IAAI;IAE1B,MAAMG,iBAAiB,GAAGT,MAAM,CAACG,MAAM;IACvC,IAAI;MACFF,SAAS,GAAG,IAAI;MAChBD,MAAM,CAACG,MAAM,GAAGD,mBAAmB;MACnC,OAAOM,OAAO,CAACH,MAAM,EAAEC,IAAI,EAAET,QAAQ,CAAC;IACxC,CAAC,SAAS;MACRI,SAAS,GAAG,KAAK;MACjBD,MAAM,CAACG,MAAM,GAAGM,iBAAiB;IACnC;EACF,CAAC;AACH;AAEA,SAASD,OAAOA,CAACH,MAAM,EAAEK,SAAS,EAAEb,QAAQ,EAAE;EAC5C,MAAMc,MAAM,GAAGN,MAAM,CAACO,SAAS,CAACF,SAAS,EAAEb,QAAQ,CAAC;EAEpD,IAAIc,MAAM,KAAK,IAAI,EAAE,OAAOD,SAAS;EAErC,MAAM;IAAEJ,IAAI;IAAER;EAAI,CAAC,GAAGa,MAAM;EAC5B,IAAIb,GAAG,EAAE;IACPT,IAAI,CAACQ,QAAQ,CAAC,GAAGC,GAAG;IACpBN,uBAAuB,CAAC,CAAC;EAC3B;EACA,OAAOc,IAAI;AACb;AAEAO,OAAO,CAACC,QAAQ,GAAG,SAASA,QAAQA,CAACT,MAAM,EAAEU,IAAI,GAAG,CAAC,CAAC,EAAE;EAAA,IAAAC,gBAAA;EACtD,IAAI5B,aAAa,EAAEA,aAAa,CAAC,CAAC;EAElCA,aAAa,GAAGH,OAAO,CACqBmB,aAAa,CAAEa,IAAI,CAAC,IAAI,EAAEZ,MAAM,CAAC,EAC3E;IACEa,IAAI,GAAAF,gBAAA,GAAED,IAAI,CAACI,UAAU,YAAAH,gBAAA,GAAIX,MAAM,CAACe,oBAAoB,CAAC,CAAC;IACtDC,iBAAiB,EAAE;EACrB,CACF,CAAC;EAEDhB,MAAM,CAACiB,UAAU,CAACP,IAAI,CAAC;AACzB,CAAC;AAEDF,OAAO,CAACU,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;EACjC,IAAInC,aAAa,EAAEA,aAAa,CAAC,CAAC;AACpC,CAAC", "ignoreList": []}