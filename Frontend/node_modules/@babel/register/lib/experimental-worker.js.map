{"version": 3, "names": ["major", "minor", "process", "versions", "node", "split", "map", "Number", "Error", "hook", "require", "WorkerClient", "client", "register", "opts", "module", "exports", "Object", "assign", "revert", "default", "__esModule", "isInRegisterWorker"], "sources": ["../src/experimental-worker.js"], "sourcesContent": ["// TODO: Move this file to index.js in Babel 8\n\n\"use strict\";\n\nconst [major, minor] = process.versions.node.split(\".\").map(Number);\n\nif (major < 12 || (major === 12 && minor < 3)) {\n  throw new Error(\n    \"@babel/register/experimental-worker requires Node.js >= 12.3.0\",\n  );\n}\n\nconst hook = require(\"./hook.js\");\nconst { WorkerClient } = require(\"./worker-client.js\");\n\nlet client;\nfunction register(opts) {\n  client ||= new WorkerClient();\n  return hook.register(client, opts);\n}\n\nmodule.exports = Object.assign(register, {\n  revert: hook.revert,\n  default: register,\n  __esModule: true,\n});\n\nif (!require(\"./is-in-register-worker.js\").isInRegisterWorker) {\n  register();\n}\n"], "mappings": "AAEA,YAAY;;AAEZ,MAAM,CAACA,KAAK,EAAEC,KAAK,CAAC,GAAGC,OAAO,CAACC,QAAQ,CAACC,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAACC,MAAM,CAAC;AAEnE,IAAIP,KAAK,GAAG,EAAE,IAAKA,KAAK,KAAK,EAAE,IAAIC,KAAK,GAAG,CAAE,EAAE;EAC7C,MAAM,IAAIO,KAAK,CACb,gEACF,CAAC;AACH;AAEA,MAAMC,IAAI,GAAGC,OAAO,CAAC,WAAW,CAAC;AACjC,MAAM;EAAEC;AAAa,CAAC,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAEtD,IAAIE,MAAM;AACV,SAASC,QAAQA,CAACC,IAAI,EAAE;EACtBF,MAAM,KAANA,MAAM,GAAK,IAAID,YAAY,CAAC,CAAC;EAC7B,OAAOF,IAAI,CAACI,QAAQ,CAACD,MAAM,EAAEE,IAAI,CAAC;AACpC;AAEAC,MAAM,CAACC,OAAO,GAAGC,MAAM,CAACC,MAAM,CAACL,QAAQ,EAAE;EACvCM,MAAM,EAAEV,IAAI,CAACU,MAAM;EACnBC,OAAO,EAAEP,QAAQ;EACjBQ,UAAU,EAAE;AACd,CAAC,CAAC;AAEF,IAAI,CAACX,OAAO,CAAC,4BAA4B,CAAC,CAACY,kBAAkB,EAAE;EAC7DT,QAAQ,CAAC,CAAC;AACZ", "ignoreList": []}