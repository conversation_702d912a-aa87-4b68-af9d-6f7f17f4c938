{"hash": "dc475fd8", "configHash": "af6b6e42", "lockfileHash": "d3e3fc16", "browserHash": "503324fa", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "a83a5875", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "23e33960", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "af88bd84", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "5cb4096b", "needsInterop": true}, "@react-native-async-storage/async-storage": {"src": "../../@react-native-async-storage/async-storage/lib/module/index.js", "file": "@react-native-async-storage_async-storage.js", "fileHash": "dc95b484", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "216accc3", "needsInterop": false}, "chart.js/auto": {"src": "../../chart.js/auto/auto.js", "file": "chart__js_auto.js", "fileHash": "4e1f8834", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "237d3265", "needsInterop": false}, "date-fns": {"src": "../../date-fns/index.js", "file": "date-fns.js", "fileHash": "261dc6c3", "needsInterop": false}, "date-fns/locale/en-US": {"src": "../../date-fns/locale/en-US.js", "file": "date-fns_locale_en-US.js", "fileHash": "032c7dcc", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "46ef8bbf", "needsInterop": false}, "react-big-calendar": {"src": "../../react-big-calendar/dist/react-big-calendar.esm.js", "file": "react-big-calendar.js", "fileHash": "ec149d83", "needsInterop": false}, "react-custom-scrollbars-2": {"src": "../../react-custom-scrollbars-2/lib/index.js", "file": "react-custom-scrollbars-2.js", "fileHash": "01820d35", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "67e309ec", "needsInterop": true}, "react-icons/ai": {"src": "../../react-icons/ai/index.mjs", "file": "react-icons_ai.js", "fileHash": "1ee7342d", "needsInterop": false}, "react-icons/fi": {"src": "../../react-icons/fi/index.mjs", "file": "react-icons_fi.js", "fileHash": "7fefed07", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "414abe6f", "needsInterop": false}, "react-spinners/ClipLoader": {"src": "../../react-spinners/ClipLoader.js", "file": "react-spinners_ClipLoader.js", "fileHash": "77606d0e", "needsInterop": true}, "recharts": {"src": "../../recharts/es6/index.js", "file": "recharts.js", "fileHash": "4989c21d", "needsInterop": false}, "styled-components": {"src": "../../styled-components/dist/styled-components.browser.esm.js", "file": "styled-components.js", "fileHash": "b1f9178e", "needsInterop": false}, "swiper/modules": {"src": "../../swiper/modules/index.mjs", "file": "swiper_modules.js", "fileHash": "97ca56e4", "needsInterop": false}, "swiper/react": {"src": "../../swiper/swiper-react.mjs", "file": "swiper_react.js", "fileHash": "d09a2529", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "d5d41a12", "needsInterop": false}}, "chunks": {"chunk-CXZHJKVX": {"file": "chunk-CXZHJKVX.js"}, "chunk-KUR4C64Q": {"file": "chunk-KUR4C64Q.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-K773IDTU": {"file": "chunk-K773IDTU.js"}, "chunk-IEEFXWTP": {"file": "chunk-IEEFXWTP.js"}, "chunk-KDCVS43I": {"file": "chunk-KDCVS43I.js"}, "chunk-RLJ2RCJQ": {"file": "chunk-RLJ2RCJQ.js"}, "chunk-FHWRLMHA": {"file": "chunk-FHWRLMHA.js"}, "chunk-DC5AMYBS": {"file": "chunk-DC5AMYBS.js"}}}