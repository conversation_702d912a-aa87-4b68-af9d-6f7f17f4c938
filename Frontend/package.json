{"name": "wholisticwellness", "homepage": "/wellness", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host --port=5173", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.1.0", "@types/chart.js": "^2.9.41", "@types/react-chartjs-2": "^2.0.2", "@types/styled-components": "^5.1.34", "axios": "^1.7.8", "chart.js": "^4.4.7", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.462.0", "postcss-loader": "^8.1.1", "react": "^18.3.1", "react-big-calendar": "^1.18.0", "react-chartjs-2": "^5.3.0", "react-custom-scrollbars-2": "^4.5.0", "react-dom": "^18.3.1", "react-icons": "^5.3.0", "react-router-dom": "^7.0.1", "react-spinners": "^0.15.0", "recharts": "^2.15.1", "styled-components": "^6.1.13", "swiper": "^11.1.15", "tailwind-merge": "^3.3.0"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "typescript": "~5.6.2", "typescript-eslint": "^8.15.0", "vite": "^6.0.1"}}